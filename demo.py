# STREAMLIT APP FOR LOCAL USE

import streamlit as st
import os

# Set environment variables to prevent PyTorch/Streamlit conflicts
os.environ['STREAMLIT_SERVER_FILE_WATCHER_TYPE'] = 'none'
os.environ['STREAMLIT_RUNNER_MAGIC_ENABLED'] = 'false'

# Fix PyTorch + Streamlit compatibility issue
try:
    import torch
    torch.set_num_threads(1)
    # Additional fix for Streamlit file watcher conflicts
    import sys
    if 'torch' in sys.modules:
        # Prevent Streamlit from inspecting torch modules
        torch._C._set_print_file_and_line(False)
except (ImportError, AttributeError):
    pass  # PyTorch not installed or method not available

from src.app.main import SyntheticData

st.set_page_config(page_title="Synthetic Data Generator", layout="wide")
st.title("🚀 Synthetic Data Generation Demo")

# Add info about potential warnings and data realism
with st.expander("ℹ️ About Warnings & Data Quality"):
    st.markdown("""
    **PyTorch/Streamlit Compatibility Warning**: You may see warnings about `torch.classes` in the terminal.
    These are harmless compatibility warnings between Streamlit and PyTorch and do not affect functionality.

    **Pipeline Types**:
    - **Standard Pipeline**: Generates all three tables jointly with relationships
    - **Conditional Pipeline**: Uses real media data to generate performance + conversions tables

    **Realistic Missing Data**: Empty cells in the synthetic data reflect real data patterns.
    The system analyzes missing data patterns from your real dataset and reproduces them in synthetic data
    to maintain realism (e.g., if 15% of 'mediarow_goal_name' values are missing in real data,
    approximately 15% will be missing in synthetic data too).
    """)

# State for loading bars and warnings
if 'is_training' not in st.session_state:
    st.session_state['is_training'] = False
if 'is_generating' not in st.session_state:
    st.session_state['is_generating'] = False
if 'output' not in st.session_state:
    st.session_state['output'] = None

# Check if models exist
models_exist = os.path.exists("models_local/multi_table_gan.pth") and os.path.exists("models_local/preprocessor.pkl")
conditional_models_exist = os.path.exists("models_local/conditional_gan.pth")

# Create tabs for different pipeline types
tab1, tab2 = st.tabs(["📊 Standard Pipeline", "🎯 Conditional Pipeline"])

with tab1:
    st.header("Standard Multi-Table Generation")
    st.info("Generates all three tables (main, performance, conversions) jointly with relationships")

    col1, col2 = st.columns([2, 1])

    with col2:
        if models_exist:
            st.success("✅ Standard models found!")
        else:
            st.warning("⚠️ No standard models found")

    with col1:
        # Train/Retrain button
        train_button_text = "Retrain Standard Models" if models_exist else "Train Standard Models"
        st.markdown("<span style='color:orange'><b>Note:</b> Training can take 5-15 minutes.</span>", unsafe_allow_html=True)
        if st.button(train_button_text, key="standard_train"):
            st.warning("Training will take a while. Please be patient.")
            st.session_state['is_training'] = True
            with st.spinner("Training in progress..."):
                # Call the main pipeline for training
                payload = {
                    'run_type': 'training',
                    'client_id': None,
                    'campaign_id': None,
                    'mediaplan_id': None
                }
                sd = SyntheticData()
                output = sd.main(payload)
                st.session_state['output'] = output
            st.success("Training complete!")
            st.session_state['is_training'] = False
            st.rerun()  # Refresh to update model status

        # Generate button - only enabled if models exist
        if models_exist:
            if st.button("Generate Synthetic Data (Quick)", key="standard_generate"):
                st.info("Using existing trained models for quick generation (no training)...")
                st.session_state['is_generating'] = True
                with st.spinner("Generating synthetic data..."):
                    # Use generation-only pipeline
                    payload = {
                        'run_type': 'generation',  # Generation only - no database connection
                        'client_id': None,
                        'campaign_id': None,
                        'mediaplan_id': None
                    }
                    sd = SyntheticData()
                    output = sd.main(payload)
                    st.session_state['output'] = output

                st.success("Synthetic data generated!")
                st.session_state['is_generating'] = False
        else:
            st.button("Generate Synthetic Data (Quick)", disabled=True, help="Train models first", key="standard_generate_disabled")

        # Full Pipeline button - always available
        st.markdown("---")  # Add separator
        st.markdown("<span style='color:orange'><b>Note:</b> Full pipeline includes fresh training and can take 15-20 minutes.</span>", unsafe_allow_html=True)
        if st.button("🚀 Run Full Standard Pipeline", key="standard_full"):
            st.warning("Full pipeline will take 15-20 minutes. This includes fresh data loading, training, generation, and evaluation.")
            st.session_state['is_generating'] = True
            with st.spinner("Running complete pipeline (training + generation + evaluation)..."):
                # Use full pipeline run type
                payload = {
                    'run_type': 'full_pipeline',  # Complete pipeline with all steps
                    'client_id': None,
                    'campaign_id': None,
                    'mediaplan_id': None
                }
                sd = SyntheticData()
                output = sd.main(payload)
                st.session_state['output'] = output
            st.success("Full pipeline completed!")
            st.session_state['is_generating'] = False

with tab2:
    st.header("Conditional Generation: Media → Performance + Conversions")
    st.info("Uses real media dimension data as input to generate realistic performance and conversions tables")

    col1, col2 = st.columns([2, 1])

    with col2:
        if conditional_models_exist:
            st.success("✅ Conditional models found!")
        else:
            st.warning("⚠️ No conditional models found")

    with col1:
        # Conditional Train button
        conditional_train_text = "Retrain Conditional Models" if conditional_models_exist else "Train Conditional Models"
        st.markdown("<span style='color:orange'><b>Note:</b> Conditional training can take 5-15 minutes.</span>", unsafe_allow_html=True)
        if st.button(conditional_train_text, key="conditional_train"):
            st.warning("Conditional training will take a while. Please be patient.")
            st.session_state['is_training'] = True
            with st.spinner("Training conditional model..."):
                payload = {
                    'run_type': 'conditional_training',
                    'client_id': None,
                    'campaign_id': None,
                    'mediaplan_id': None
                }
                sd = SyntheticData()
                output = sd.main(payload)
                st.session_state['output'] = output
            st.success("Conditional training complete!")
            st.session_state['is_training'] = False
            st.rerun()

        # Conditional Generate button
        if conditional_models_exist:
            if st.button("Generate Conditional Data (Quick)", key="conditional_generate"):
                st.info("Using existing conditional models for quick generation...")
                st.session_state['is_generating'] = True
                with st.spinner("Generating conditional synthetic data..."):
                    payload = {
                        'run_type': 'conditional_generation',
                        'client_id': None,
                        'campaign_id': None,
                        'mediaplan_id': None
                    }
                    sd = SyntheticData()
                    output = sd.main(payload)
                    st.session_state['output'] = output
                st.success("Conditional synthetic data generated!")
                st.session_state['is_generating'] = False
        else:
            st.button("Generate Conditional Data (Quick)", disabled=True, help="Train conditional models first", key="conditional_generate_disabled")

        # Conditional Full Pipeline
        st.markdown("---")
        st.markdown("<span style='color:orange'><b>Note:</b> Full conditional pipeline can take 15-20 minutes.</span>", unsafe_allow_html=True)
        if st.button("🚀 Run Full Conditional Pipeline", key="conditional_full"):
            st.warning("Full conditional pipeline will take 15-20 minutes. This includes training, generation, and evaluation.")
            st.session_state['is_generating'] = True
            with st.spinner("Running complete conditional pipeline..."):
                payload = {
                    'run_type': 'conditional_pipeline',
                    'client_id': None,
                    'campaign_id': None,
                    'mediaplan_id': None
                }
                sd = SyntheticData()
                output = sd.main(payload)
                st.session_state['output'] = output
            st.success("Full conditional pipeline completed!")
            st.session_state['is_generating'] = False

# Show outputs if available
output = st.session_state.get('output')
# Support both OutputSchema object and dict for compatibility
if output:
    # Debug: Show what we actually got
    with st.expander("🔍 Debug: What did the pipeline return?"):
        st.write(f"**Output type:** {type(output)}")
        if isinstance(output, dict):
            st.write(f"**Dict keys:** {list(output.keys())}")
        else:
            st.write(f"**Object attributes:** {[attr for attr in dir(output) if not attr.startswith('_')]}")

        # Show status if available
        status = output.get('status') if isinstance(output, dict) else getattr(output, 'status', 'unknown')
        st.write(f"**Status:** {status}")

        # Show error if any
        error = output.get('error') if isinstance(output, dict) else getattr(output, 'error', None)
        if error:
            st.error(f"**Error:** {error}")

    # Load evaluation results from file system (where they're actually stored)
    def load_evaluation_results():
        """Load evaluation results from the file system"""
        try:
            import json
            import os

            # Try multiple possible locations
            possible_paths = [
                'data/evaluation/evaluation_results.json',
                'evaluation_results/evaluation_results.json',
                'data/evaluation/evaluation_results.pkl'
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    if path.endswith('.json'):
                        with open(path, 'r') as f:
                            return json.load(f)
                    elif path.endswith('.pkl'):
                        import pickle
                        with open(path, 'rb') as f:
                            return pickle.load(f)
            return None
        except Exception as e:
            st.error(f"Error loading evaluation results: {e}")
            return None

    # Load evaluation results from file system
    eval_results = load_evaluation_results()

    # Add refresh button for evaluation results
    col1, col2 = st.columns([3, 1])
    with col2:
        if st.button("🔄 Refresh Evaluation"):
            eval_results = load_evaluation_results()
            st.rerun()

    if eval_results:
        st.header("📊 Data Quality Results")

        # Extract results from the enhanced format
        results_data = eval_results.get('results', eval_results)

        # Show key metrics in a simple layout
        if 'integrity_comparison' in results_data and 'cross_table' in results_data['integrity_comparison']:
            cross_table = results_data['integrity_comparison']['cross_table']
            col1, col2, col3 = st.columns(3)

            # Extract integrity scores
            client_score = cross_table.get('client_id_consistency', {}).get('integrity_score', 0) * 100
            campaign_score = cross_table.get('campaign_id_consistency', {}).get('integrity_score', 0) * 100
            mediarow_score = cross_table.get('mediarow_id_consistency', {}).get('integrity_score', 0) * 100

            col1.metric("Client ID Integrity", f"{client_score:.1f}%")
            col2.metric("Campaign ID Integrity", f"{campaign_score:.1f}%")
            col3.metric("Media Row ID Integrity", f"{mediarow_score:.1f}%")

        # Show metadata if available
        metadata = eval_results.get('metadata', {})
        if metadata:
            st.info(f"📅 Evaluation completed: {metadata.get('timestamp', 'Unknown time')}")

        # Show raw results in expandable section
        with st.expander("📋 View Full Evaluation Results"):
            st.json(results_data)

        st.divider()
    else:
        st.info("📊 No evaluation results found. Run the full pipeline or evaluation to generate quality metrics.")

    # Display synthetic data preview with tabs (as requested)
    gen_results = output.get('generation_results') if isinstance(output, dict) else getattr(output, 'generation_results', None)
    if gen_results and gen_results.get('generated_data'):
        st.header("📋 Synthetic Data Preview & Download")
        data = gen_results['generated_data']

        # Create tabs for each table (keeping the tabbed layout you liked)
        main_tab, perf_tab, conv_tab = st.tabs(["📊 Main", "📈 Performance", "🎯 Conversions"])

        # Helper function to display table data
        def display_table(tab, title, df, filename):
            with tab:
                st.subheader(title)
                st.write(f"**Shape:** {df.shape[0]:,} rows × {df.shape[1]} columns")
                st.dataframe(df.head(20), use_container_width=True)
                csv = df.to_csv(index=False)
                st.download_button(
                    label=f"📥 Download {filename}",
                    data=csv,
                    file_name=filename,
                    mime="text/csv"
                )

        display_table(main_tab, "Main Table", data['main'], "main_synthetic.csv")
        display_table(perf_tab, "Performance Table", data['performance'], "performance_synthetic.csv")
        display_table(conv_tab, "Conversions Table", data['conversions'], "conversions_synthetic.csv")
    else:
        st.info("Click 'Generate Synthetic Data' to preview and download synthetic tables.")

