# STREAMLIT APP FOR LOCAL USE

import streamlit as st
import os

# Set environment variables to prevent PyTorch/Streamlit conflicts
os.environ['STREAMLIT_SERVER_FILE_WATCHER_TYPE'] = 'none'
os.environ['STREAMLIT_RUNNER_MAGIC_ENABLED'] = 'false'

# Fix PyTorch + Streamlit compatibility issue
try:
    import torch
    torch.set_num_threads(1)
    # Additional fix for Streamlit file watcher conflicts
    import sys
    if 'torch' in sys.modules:
        # Prevent Streamlit from inspecting torch modules
        torch._C._set_print_file_and_line(False)
except (ImportError, AttributeError):
    pass  # PyTorch not installed or method not available

from src.app.main import SyntheticData

st.set_page_config(page_title="Synthetic Data Generator", layout="wide")
st.title("Synthetic Data Generation Demo")

# Add info about potential warnings and data realism
with st.expander("ℹ️ About Warnings & Data Quality"):
    st.markdown("""
    **PyTorch/Streamlit Compatibility Warning**: You may see warnings about `torch.classes` in the terminal.
    These are harmless compatibility warnings between Streamlit and PyTorch and do not affect functionality.

    **Expected Behavior**:
    - **Train Models**: Full pipeline (5-15 minutes) - loads real data, trains models, generates synthetic data
    - **Generate Quick**: Uses existing models (1-2 minutes) - skips training, generates synthetic data only
    - **Full Pipeline**: Complete fresh pipeline (15-20 minutes) - fresh training, generation, and evaluation

    **Realistic Missing Data**: Empty cells in the synthetic data reflect real data patterns.
    The system analyzes missing data patterns from your real dataset and reproduces them in synthetic data
    to maintain realism (e.g., if 15% of 'mediarow_goal_name' values are missing in real data,
    approximately 15% will be missing in synthetic data too).
    """)

# State for loading bars and warnings
if 'is_training' not in st.session_state:
    st.session_state['is_training'] = False
if 'is_generating' not in st.session_state:
    st.session_state['is_generating'] = False
if 'output' not in st.session_state:
    st.session_state['output'] = None

st.sidebar.header("Actions")

# Check if models exist
models_exist = os.path.exists("models_local/multi_table_gan.pth") and os.path.exists("models_local/preprocessor.pkl")

if models_exist:
    st.sidebar.success("✅ Trained models found!")
    st.sidebar.markdown("You can generate synthetic data directly or retrain the models.")
else:
    st.sidebar.warning("⚠️ No trained models found. You need to train first.")

# Train/Retrain button
train_button_text = "Retrain Models" if models_exist else "Train Models"
st.sidebar.markdown("<span style='color:orange'><b>Note:</b> Training can take anywhere between 5 - 15 minutes depending on your machine.</span>", unsafe_allow_html=True)
if st.sidebar.button(train_button_text):
    st.warning("Training will take a while. Please be patient.")
    st.session_state['is_training'] = True
    with st.spinner("Training in progress..."):
        # Call the main pipeline for training
        payload = {
            'run_type': 'training',
            'client_id': None,
            'campaign_id': None,
            'mediaplan_id': None
        }
        sd = SyntheticData()
        output = sd.main(payload)
        st.session_state['output'] = output
    st.success("Training complete!")
    st.session_state['is_training'] = False
    st.rerun()  # Refresh to update model status

# Generate button - only enabled if models exist
if models_exist:
    if st.sidebar.button("Generate Synthetic Data (Quick)"):
        st.info("Using existing trained models for quick generation (no training)...")
        st.session_state['is_generating'] = True
        with st.spinner("Generating synthetic data..."):
            # Use generation-only pipeline
            payload = {
                'run_type': 'generation',  # Generation only - no database connection
                'client_id': None,
                'campaign_id': None,
                'mediaplan_id': None
            }
            sd = SyntheticData()
            output = sd.main(payload)
            st.session_state['output'] = output

        st.success("Synthetic data generated!")
        st.session_state['is_generating'] = False
else:
    st.sidebar.button("Generate Synthetic Data (Quick)", disabled=True, help="Train models first")

# Full Pipeline button - always available
st.sidebar.markdown("---")  # Add separator
st.sidebar.markdown("<span style='color:orange'><b>Note:</b> Full pipeline includes fresh training and can take 15-20 minutes.</span>", unsafe_allow_html=True)
if st.sidebar.button("🚀 Run Full Pipeline"):
    st.warning("Full pipeline will take 15-20 minutes. This includes fresh data loading, training, generation, and evaluation.")
    st.session_state['is_generating'] = True
    with st.spinner("Running complete pipeline (training + generation + evaluation)..."):
        # Use full pipeline run type
        payload = {
            'run_type': 'full_pipeline',  # Complete pipeline with all steps
            'client_id': None,
            'campaign_id': None,
            'mediaplan_id': None
        }
        sd = SyntheticData()
        output = sd.main(payload)
        st.session_state['output'] = output
    st.success("Full pipeline completed!")
    st.session_state['is_generating'] = False

# Show outputs if available
output = st.session_state.get('output')
# Support both OutputSchema object and dict for compatibility
if output:
    # Debug: Show what we actually got
    with st.expander("🔍 Debug: What did the pipeline return?"):
        st.write(f"**Output type:** {type(output)}")
        if isinstance(output, dict):
            st.write(f"**Dict keys:** {list(output.keys())}")
        else:
            st.write(f"**Object attributes:** {[attr for attr in dir(output) if not attr.startswith('_')]}")

        # Show status if available
        status = output.get('status') if isinstance(output, dict) else getattr(output, 'status', 'unknown')
        st.write(f"**Status:** {status}")

        # Show error if any
        error = output.get('error') if isinstance(output, dict) else getattr(output, 'error', None)
        if error:
            st.error(f"**Error:** {error}")

    # Load evaluation results from file system (where they're actually stored)
    def load_evaluation_results():
        """Load evaluation results from the file system"""
        try:
            import json
            import os

            # Try multiple possible locations
            possible_paths = [
                'data/evaluation/evaluation_results.json',
                'evaluation_results/evaluation_results.json',
                'data/evaluation/evaluation_results.pkl'
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    if path.endswith('.json'):
                        with open(path, 'r') as f:
                            return json.load(f)
                    elif path.endswith('.pkl'):
                        import pickle
                        with open(path, 'rb') as f:
                            return pickle.load(f)
            return None
        except Exception as e:
            st.error(f"Error loading evaluation results: {e}")
            return None

    # Load evaluation results from file system
    eval_results = load_evaluation_results()

    # Add refresh button for evaluation results
    col1, col2 = st.columns([3, 1])
    with col2:
        if st.button("🔄 Refresh Evaluation"):
            eval_results = load_evaluation_results()
            st.rerun()

    if eval_results:
        st.header("📊 Data Quality Results")

        # Extract results from the enhanced format
        results_data = eval_results.get('results', eval_results)

        # Show key metrics in a simple layout
        if 'integrity_comparison' in results_data and 'cross_table' in results_data['integrity_comparison']:
            cross_table = results_data['integrity_comparison']['cross_table']
            col1, col2, col3 = st.columns(3)

            # Extract integrity scores
            client_score = cross_table.get('client_id_consistency', {}).get('integrity_score', 0) * 100
            campaign_score = cross_table.get('campaign_id_consistency', {}).get('integrity_score', 0) * 100
            mediarow_score = cross_table.get('mediarow_id_consistency', {}).get('integrity_score', 0) * 100

            col1.metric("Client ID Integrity", f"{client_score:.1f}%")
            col2.metric("Campaign ID Integrity", f"{campaign_score:.1f}%")
            col3.metric("Media Row ID Integrity", f"{mediarow_score:.1f}%")

        # Show metadata if available
        metadata = eval_results.get('metadata', {})
        if metadata:
            st.info(f"📅 Evaluation completed: {metadata.get('timestamp', 'Unknown time')}")

        # Show raw results in expandable section
        with st.expander("📋 View Full Evaluation Results"):
            st.json(results_data)

        st.divider()
    else:
        st.info("📊 No evaluation results found. Run the full pipeline or evaluation to generate quality metrics.")

    # Display synthetic data preview with tabs (as requested)
    gen_results = output.get('generation_results') if isinstance(output, dict) else getattr(output, 'generation_results', None)
    if gen_results and gen_results.get('generated_data'):
        st.header("📋 Synthetic Data Preview & Download")
        data = gen_results['generated_data']

        # Create tabs for each table (keeping the tabbed layout you liked)
        main_tab, perf_tab, conv_tab = st.tabs(["📊 Main", "📈 Performance", "🎯 Conversions"])

        # Helper function to display table data
        def display_table(tab, title, df, filename):
            with tab:
                st.subheader(title)
                st.write(f"**Shape:** {df.shape[0]:,} rows × {df.shape[1]} columns")
                st.dataframe(df.head(20), use_container_width=True)
                csv = df.to_csv(index=False)
                st.download_button(
                    label=f"📥 Download {filename}",
                    data=csv,
                    file_name=filename,
                    mime="text/csv"
                )

        display_table(main_tab, "Main Table", data['main'], "main_synthetic.csv")
        display_table(perf_tab, "Performance Table", data['performance'], "performance_synthetic.csv")
        display_table(conv_tab, "Conversions Table", data['conversions'], "conversions_synthetic.csv")
    else:
        st.info("Click 'Generate Synthetic Data' to preview and download synthetic tables.")

