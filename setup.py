from setuptools import setup, find_packages

setup(
    name="synthetic_gan",
    version="0.1.0",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    package_data={
        "": ["*.py", "*.pkl", "*.csv"]  # Include non-Python files
    },
    python_requires=">=3.8",
    install_requires=[
        'torch>=1.10.0',  # Updated minimum version
        'numpy>=1.21.0',
        'pandas>=1.3.0',
        'scikit-learn>=1.0.0',
        'matplotlib>=3.5.0',
        'seaborn>=0.11.0',
        'scipy>=1.7.0'  # Added for statistical tests
    ],
    entry_points={
        'console_scripts': [
            'synthetic-gan-train=synthetic_gan.training.train:main',
            'synthetic-gan-generate=synthetic_gan.generation.generate:main',
            'synthetic-gan-eval=synthetic_gan.evaluation.evaluate:main'
        ]
    }
)