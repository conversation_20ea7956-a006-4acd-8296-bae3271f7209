import logging
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from typing import Any, Dict, List, Optional

import boto3
from botocore.exceptions import ClientError

from src.constants import AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_SESSION_TOKEN
from src.utils.errors import S3Error


class S3Client:
    def __str__(self) -> str:
        return "S3"

    def __init__(self, region_name: str = "eu-west-1"):
        self.logger = logging.getLogger(str(self))
        self.resource = boto3.resource(
            "s3",
            region_name=region_name,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            aws_session_token=AWS_SESSION_TOKEN,
        )
        self.client = boto3.client(
            "s3",
            region_name=region_name,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            aws_session_token=AWS_SESSION_TOKEN,
        )

    def upload_file(self, file_path: str, bucket_name: str, object_key: str) -> bool:
        """Upload file from path to S3
        Args:
            file_path (str): path of the file
            bucket_name (str): name of the bucket
            object_key (str): key of the object
        Raises:
            S3Error: Error related to S3 upload procedures
        Returns:
            bool: success/fail
        """
        try:
            self.client.upload_file(file_path, bucket_name, object_key)
            return True
        except ClientError:
            raise S3Error("Error uploading file to S3: {e}")

    def upload_object(self, obj: Any, bucket_name: str, object_key: str) -> bool:
        """Uploads object to S3 folder
        Args:
            obj (Any): object to upload
            bucket_name (str): name of the budget
            object_key (str): key of the object
        Raises:
            S3Error: Error related to S3 upload procedures
        Returns:
            bool: success/fail
        """
        try:
            self.resource.Object(bucket_name, object_key).put(Body=obj)
            return True
        except ClientError as e:
            raise S3Error(f"Error uploading object to S3: {e}")

    def download_file(
        self, bucket_name: str, object_key: str, file_path: str
    ) -> Optional[str]:
        """Downloads file to path
        Args:
            bucket_name (str): name of the bucket
            object_key (str): key of the object
            file_path (str): path where to download
        Raises:
            S3Error: Error related to S3 download procedures
        Returns:
            Optional[str]: success/fail
        """
        try:
            self.client.download_file(bucket_name, object_key, file_path)
            return True
        except ClientError as e:
            raise S3Error(f"Error downloading file from S3: {e}")

    def download_object(self, bucket_name: str, object_key: str) -> Optional[Any]:
        """Downloads file to python object
        Args:
            bucket_name (str): bucket name
            object_key (str): key of the object
        Raises:
            S3Error: Error related to S3 download procedures
        Returns:
            Optional[Any]: object
        """
        try:
            obj = self.resource.Object(bucket_name, object_key).get()["Body"].read()
            return obj
        except ClientError as e:
            raise S3Error(f"Error downloading object from S3: {e}")

    def download_objects(
        self, bucket_name: str, object_keys: List[str]
    ) -> Dict[str, Optional[Any]]:
        """Downloads multiple files to python objects in parallel
        Args:
            bucket_name (str): bucket name
            object_keys (List[str]): list of object keys to download
        Raises:
            S3Error: Error related to S3 download procedures
        Returns:
            Dict[str, Optional[Any]]: dictionary with object keys as keys and downloaded objects as values
        """
        results = {}

        with ThreadPoolExecutor() as executor:
            future_to_key = {
                executor.submit(
                    self.download_object, bucket_name, object_key
                ): object_key
                for object_key in object_keys
            }
            for future in as_completed(future_to_key):
                object_key = future_to_key[future]
                try:
                    results[object_key] = future.result()
                except S3Error as e:
                    self.logger.warning(f"Failed to download {object_key}: {e}")
                    results[object_key] = None

        return results

    def delete_object(self, bucket_name: str, object_key: str) -> bool:
        """Delets object from path
        Args:
            bucket_name (str): name of the bucket
            object_key (str): key of the object
        Raises:
            S3Error: Error related to S3 download procedures
        Returns:
            bool: success/fail
        """
        try:
            self.client.delete_object(Bucket=bucket_name, Key=object_key)
            return True
        except ClientError as e:
            raise S3Error(f"Error deleting object from S3: {e}")