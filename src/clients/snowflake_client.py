from src.config import settings


class SnowflakeClient:

    def __init__(self):
        self.params = {
            "account": settings.SNOWFLAKE["account"],
            "user": settings.<PERSON>N<PERSON><PERSON><PERSON>KE["user"],
            "private_key_file": settings.SNOWFLAKE["private_key_file"],
            "warehouse": settings.SNOWFLAKE["warehouse"],
            "database": settings.SNOWFLAKE["database"],
            "schema": settings.SNOWFLAKE["schema"],
        }
