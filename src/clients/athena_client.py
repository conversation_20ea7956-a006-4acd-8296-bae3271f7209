import io
import os
from logging import get<PERSON><PERSON><PERSON>
from typing import Any, Dict, Optional

import boto3
import numpy as np
import pandas as pd
from boto3 import Session

from src.settings import ATHENA
from src.utils.errors import <PERSON><PERSON>rror
from src.constants import AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_SESSION_TOKEN


class AthenaClient:
    def __str__(self) -> str:
        return "ATHENA-CLIENT"

    def __init__(
        self,
        athena_client: Optional[Session] = None,
        s3_resource: Optional[Session] = None,
        database: Optional[str] = ATHENA["db_name"],
        output_bucket: Optional[str] = ATHENA["output_url"],
        output_folder: Optional[str] = ATHENA["output_folder"],
        region: Optional[str] = ATHENA["region"],
    ) -> None:
        """Initializer for the Athena client class.

        If no arguments are passed, the client is inizialized for
        the environment variables found (admin user).
        Otherwise, it's possible to inject specific clients and resources,
        alongside with variables for the database and output location.

        Args:
            athena_client (Session): athena client with role
            s3_resource (Session): s3 resource with role
            database (str): target database
            output_bucket (str): output aws s3 bucket
            output_folder (str): output folder inside bucket
            region (str): aws region
        """
        self.logger = getLogger(str(self))
        self.database = database
        self.output_bucket = output_bucket
        self.output_folder = output_folder

        # Set AWS credentials as environment variables for boto3
        if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY:
            os.environ['AWS_ACCESS_KEY_ID'] = AWS_ACCESS_KEY_ID
            os.environ['AWS_SECRET_ACCESS_KEY'] = AWS_SECRET_ACCESS_KEY
            if AWS_SESSION_TOKEN:
                os.environ['AWS_SESSION_TOKEN'] = AWS_SESSION_TOKEN

        # If no specific athena client is passed, initialize it
        if not athena_client:
            self.athena_client = boto3.client("athena", region_name=region)
        else:
            self.athena_client = athena_client
        # If no specific s3 resource is passed, initialize it
        if not s3_resource:
            self.s3_resource = boto3.resource("s3", region_name=region)
        else:
            self.s3_resource = s3_resource

    @staticmethod
    def _convert_dtypes(data_type: str) -> Any:
        """Converts the data type of a column to a pandas compatible value.
        Args:
            data_type (str): The data type of the column
        Returns:
            Any: The pandas compatible value.
        """
        if data_type in ["tinyint", "smallint", "integer", "bigint"]:
            return "Int64"
        elif data_type in ["float", "double"]:
            return "float"
        elif data_type == "boolean":
            return "bool"
        elif data_type == "timestamp":
            return np.datetime64
        else:
            return "str"

    @staticmethod
    def _read_sql(filepath: str, params: Dict[str, Any] = None) -> str:
        """Reads a SQL query file and formats it.
        Args:
            filepath (str): Path to the query file
            params (Dict[str, Any], optional): Parameters to format the query. Defaults to None.
        Returns:
            str: The formatted SQL query.
        """
        with open(filepath, "r") as f:
            query = f.read()

        if not params:
            return query
        else:
            return query.format(**params)

    def _launch_query(self, q: str) -> str:
        """Launches a query to Athena.
        Args:
            q (str): The query to be executed.
        Returns:
            str: The query execution ID.
        """
        response = self.athena_client.start_query_execution(
            QueryString=q,
            QueryExecutionContext={"Database": self.database},
            ResultConfiguration={
                "OutputLocation": "s3://"
                + self.output_bucket
                + "/"
                + self.output_folder
                + "/"
            },
        )
        return response["QueryExecutionId"]

    def _get_query_status(self, query_execution_id: str) -> str:
        """Retrieves the status of a query execution.
        Args:
            query_execution_id (str): The ID of the query execution.
        Returns:
            str: The status of the query execution.
        """
        response = self.athena_client.get_query_execution(
            QueryExecutionId=query_execution_id
        )
        status = response["QueryExecution"]["Status"]["State"]

        if status == "FAILED":
            return (
                status,
                response["QueryExecution"]["Status"]["StateChangeReason"],
            )
        else:
            return status, ""

    def _wait_end(self, query_execution_id: str) -> None:
        """Waits for the query execution to finish.

        Args:
            query_execution_id (str): The ID of the query execution.

        Raises:
            AthenaError: If the query is cancelled or failed.
        """
        query_status, _ = self._get_query_status(query_execution_id=query_execution_id)
        while query_status in ["QUEUED", "RUNNING", None]:
            query_status, error = self._get_query_status(
                query_execution_id=query_execution_id
            )
            if query_status == "CANCELLED":
                raise AthenaError("Athena query cancelled")
            elif query_status == "FAILED":
                raise AthenaError(f"Athena query failed with error: {error}")

    def _get_data(self, query_execution_id: str, **kwargs) -> pd.DataFrame:
        """Extracts data after a query has been executed.

        Args:
            query_execution_id (str): The ID of the query execution.

        Returns:
            pd.DataFrame: The result of the query.
        """

        # Extract data from S3
        query_result = (
            self.s3_resource.Bucket(self.output_bucket)
            .Object(key=self.output_folder + "/" + query_execution_id + ".csv")
            .get()
        )

        # Extract query results information
        athena_result = self.athena_client.get_query_results(
            QueryExecutionId=query_execution_id
        )

        if "dtype" not in kwargs:
            kwargs["dtype"] = {
                col["Name"]: self._convert_dtypes(col["Type"])
                for col in athena_result["ResultSet"]["ResultSetMetadata"]["ColumnInfo"]
            }

        return pd.read_csv(
            io.BytesIO(query_result["Body"].read()), encoding="utf8", **kwargs
        )

    def run_query(
        self,
        q: str,
        read: bool = False,
        no_res: bool = False,
        params: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> pd.DataFrame:
        """Reads and runs a query in Athena.

        Args:
            q (str): The query or path to the query.
            read (bool, optional): Whether to read the query from a file. Defaults to False.
            params (Optional[Dict[str, Any]], optional): Parameters to format the query. Defaults to None.

        Returns:
            pd.DataFrame: The result of the query.
        """
        if read:
            query = self._read_sql(filepath=q, params=params)
        else:
            query = q

        query_execution_id = self._launch_query(query)

        try:
            self._wait_end(query_execution_id)
        except AthenaError as e:
            self.logger.error(f"Query failed: {query}")
            raise e

        # Store query_execution_id
        self.query_id = query_execution_id

        if not no_res:
            return self._get_data(query_execution_id, **kwargs)