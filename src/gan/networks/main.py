from typing import List, Optional

import torch
import torch.nn as nn

class BaseNetwork(nn.Module):
    """Base class for all neural network components"""
    
    def __init__(self, device: Optional[torch.device] = None):
        super().__init__()
        self._device = device
        if device is not None:
            self.to(device)
        
    @property
    def device(self):
        return self._device
    
    @device.setter
    def device(self, value):
        self._device = value
        if value is not None:
            self.to(value)
    
    def to(self, device):
        """Move all components to device"""
        if device is not None:
            super().to(device)
            self._device = device
        return self
    
    def initialize_weights(self):
        """Initialize network weights"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)

class NetworkBuilder:
    """Helper class for building network layers"""
    
    @staticmethod
    def build_mlp(
        input_dim: int,
        hidden_dims: List[int],
        output_dim: int,
        activation: nn.Module = nn.ReLU,
        dropout: float = 0.0,
        batch_norm: bool = False,
        device: Optional[torch.device] = None
    ) -> nn.Sequential:
        """Build a multi-layer perceptron"""
        layers = []
        prev_dim = input_dim
        
        # Hidden layers
        for dim in hidden_dims:
            linear = nn.Linear(prev_dim, dim)
            if device is not None:
                linear = linear.to(device)
            layers.append(linear)
            
            if batch_norm:
                bn = nn.BatchNorm1d(dim)
                if device is not None:
                    bn = bn.to(device)
                layers.append(bn)
            
            act = activation()
            if device is not None:
                act = act.to(device)
            layers.append(act)
            
            if dropout > 0:
                dropout_layer = nn.Dropout(dropout)
                if device is not None:
                    dropout_layer = dropout_layer.to(device)
                layers.append(dropout_layer)
            prev_dim = dim
        
        # Output layer
        if output_dim is not None:
            output_linear = nn.Linear(prev_dim, output_dim)
            if device is not None:
                output_linear = output_linear.to(device)
            layers.append(output_linear)
        
        model = nn.Sequential(*layers)
        if device is not None:
            model = model.to(device)
        
        return model
    
    @staticmethod
    def build_discriminator(
        input_dim: int,
        hidden_dims: List[int] = [512, 256, 128],
        dropout: float = 0.3
    ) -> nn.Sequential:
        """Build a discriminator network"""
        layers = []
        prev_dim = input_dim
        
        for dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, dim))
            layers.append(nn.LeakyReLU(0.2))
            layers.append(nn.Dropout(dropout))
            prev_dim = dim
        
        # Output layer with sigmoid
        layers.append(nn.Linear(prev_dim, 1))
        layers.append(nn.Sigmoid())
        
        return nn.Sequential(*layers)
    
    @staticmethod
    def build_generator(
        noise_dim: int,
        hidden_dims: List[int] = [128, 256],
        output_dim: int = None,
        dropout: float = 0.2,
        device: Optional[torch.device] = None
    ) -> nn.Sequential:
        """Build a generator network"""
        layers = []
        prev_dim = noise_dim
        
        for dim in hidden_dims:
            linear = nn.Linear(prev_dim, dim)
            if device is not None:
                linear = linear.to(device)
            layers.append(linear)
            
            relu = nn.ReLU(True)
            if device is not None:
                relu = relu.to(device)
            layers.append(relu)
            
            dropout_layer = nn.Dropout(dropout)
            if device is not None:
                dropout_layer = dropout_layer.to(device)
            layers.append(dropout_layer)
            
            prev_dim = dim
        
        if output_dim is not None:
            output_linear = nn.Linear(prev_dim, output_dim)
            if device is not None:
                output_linear = output_linear.to(device)
            layers.append(output_linear)
        
        model = nn.Sequential(*layers)
        if device is not None:
            model = model.to(device)
        
        return model
