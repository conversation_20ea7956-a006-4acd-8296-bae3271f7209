from typing import List, Optional

import torch
import torch.nn as nn

from .main import BaseNetwork, NetworkBuilder

class Discriminator(BaseNetwork):
    """Discriminator network for synthetic data generation"""

    def __init__(self, input_dim: int, device: Optional[torch.device] = None):
        super().__init__(device)
        self.input_dim = input_dim

        # Create feature extractor
        self.feature_extractor = NetworkBuilder.build_mlp(
            input_dim=input_dim,
            hidden_dims=[512, 256, 128],
            output_dim=None,
            activation=nn.LeakyReLU,
            dropout=0.3,
            device=device
        )

        # Create classifier
        self.classifier = NetworkBuilder.build_mlp(
            input_dim=128,
            hidden_dims=[64],
            output_dim=1,
            activation=nn.LeakyReLU,
            dropout=0.3,
            device=device
        )
        self.classifier.add_module('sigmoid', nn.Sigmoid())

        # Initialize weights
        self.initialize_weights()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Evaluate input data"""
        # Only process numeric features
        features = self.feature_extractor(x)
        output = self.classifier(features)
        return output
