import torch
import torch.nn as nn
from typing import Optional, List
from .main import BaseNetwork, NetworkBuilder

class Generator(BaseNetwork):
    """Generator network for synthetic data generation"""

    def __init__(
        self,
        noise_dim: int,
        output_dim: int,
        categorical_mask: Optional[torch.Tensor] = None,
        device: Optional[torch.device] = None
    ):
        super().__init__(device)
        self.noise_dim = noise_dim
        self.output_dim = output_dim

        # Calculate dimensions
        num_numeric = output_dim
        num_categorical = 0
        if categorical_mask is not None:
            num_numeric = torch.sum(~categorical_mask).item()
            num_categorical = torch.sum(categorical_mask).item()

        # Handle categorical mask
        if categorical_mask is not None:
            if not isinstance(categorical_mask, torch.Tensor):
                raise ValueError("categorical_mask must be a torch.Tensor")
            self.categorical_mask = categorical_mask
        else:
            self.categorical_mask = torch.zeros(output_dim, dtype=torch.bool, device=device)

        self.categorical_indices = torch.where(self.categorical_mask)[0].tolist()

        # Create separate networks
        self.numeric_network = NetworkBuilder.build_generator(
            noise_dim=noise_dim,
            hidden_dims=[128, 256],
            output_dim=num_numeric,
            dropout=0.2,
            device=device
        )

        if num_categorical > 0:
            self.categorical_network = NetworkBuilder.build_generator(
                noise_dim=noise_dim,
                hidden_dims=[128, 256],
                output_dim=num_categorical,
                dropout=0.2,
                device=device
            )
            self.categorical_network.add_module('softmax', nn.Softmax(dim=1))
        else:
            self.categorical_network = nn.Sequential(
                nn.Identity()
            )

        # Initialize weights
        self.initialize_weights()

    def forward(self, z: torch.Tensor) -> torch.Tensor:
        """Generate synthetic data"""
        # Ensure input is on the correct device
        z = z.to(self.device)

        # Generate numeric outputs
        numeric_output = self.numeric_network(z)

        # Generate categorical outputs if needed
        if len(self.categorical_indices) > 0:
            categorical_output = self.categorical_network(z)

            # Combine numeric and categorical outputs
            output = torch.zeros(z.size(0), self.output_dim, device=z.device)
            output[:, ~self.categorical_mask] = numeric_output
            output[:, self.categorical_mask] = categorical_output

            return output
        else:
            return numeric_output

    def generate_categorical(self, batch_size: int) -> Optional[torch.Tensor]:
        """Generate categorical values for categorical columns"""
        if not self.categorical_indices:
            return None

        # Generate random values in range [-1, 1]
        categorical_noise = torch.randn(batch_size, len(self.categorical_indices), device=self.device)
        categorical_output = self.categorical_network(categorical_noise)
        return categorical_output.argmax(dim=1)
