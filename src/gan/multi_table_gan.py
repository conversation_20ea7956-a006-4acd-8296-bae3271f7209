import torch
import torch.nn as nn
import torch.optim as optim
from typing import Tuple, List, Optional, Dict
from .networks.generator import Generator
from .networks.discriminator import Discriminator
from sklearn.metrics import mutual_info_score
from scipy.stats import ks_2samp
import logging
import pandas as pd

class MultiTableGAN(nn.Module):
    """Multi-table GAN for synthetic data generation"""

    def __str__(self) -> str:
        return "MULTI-TABLE-GAN"
    def __init__(
        self,
        noise_dim: int,
        main_dim: int,
        perf_dim: int,
        conv_dim: int,
        categorical_cols_main: Optional[List[bool]] = None,
        categorical_cols_perf: Optional[List[bool]] = None,
        categorical_cols_conv: Optional[List[bool]] = None,
        learning_rate: float = 0.0002,
        device: Optional[torch.device] = None
    ):
        """Initialize multi-table GAN

        Args:
            noise_dim: Dimension of the noise vector
            main_dim: Dimension of the main table
            perf_dim: Dimension of the performance table
            conv_dim: Dimension of the conversions table
            categorical_cols_main: Boolean mask for categorical columns in main table
            categorical_cols_perf: Boolean mask for categorical columns in performance table
            categorical_cols_conv: Boolean mask for categorical columns in conversions table
            learning_rate: Learning rate for optimizers
            device: Device to use (cpu, cuda, or mps)
        """
        super().__init__()
        self.noise_dim = noise_dim
        self.device = device

        # Initialize generators with categorical masks
        self.main_gen = Generator(
            noise_dim=noise_dim,
            output_dim=main_dim,
            categorical_mask=torch.tensor(categorical_cols_main, dtype=torch.bool, device=device) if categorical_cols_main else None,
            device=device
        )

        self.perf_gen = Generator(
            noise_dim=noise_dim,
            output_dim=perf_dim,
            categorical_mask=torch.tensor(categorical_cols_perf, dtype=torch.bool, device=device) if categorical_cols_perf else None,
            device=device
        )

        self.conv_gen = Generator(
            noise_dim=noise_dim,
            output_dim=conv_dim,
            categorical_mask=torch.tensor(categorical_cols_conv, dtype=torch.bool, device=device) if categorical_cols_conv else None,
            device=device
        )

        # Initialize discriminators
        self.main_disc = Discriminator(main_dim, device=device)
        self.perf_disc = Discriminator(perf_dim, device=device)
        self.conv_disc = Discriminator(conv_dim, device=device)

        # Initialize optimizers with Adam
        self.optimizer_G = optim.Adam(
            list(self.main_gen.parameters()) +
            list(self.perf_gen.parameters()) +
            list(self.conv_gen.parameters()),
            lr=learning_rate,
            betas=(0.5, 0.999)
        )

        self.optimizer_D = optim.Adam(
            list(self.main_disc.parameters()) +
            list(self.perf_disc.parameters()) +
            list(self.conv_disc.parameters()),
            lr=learning_rate,
            betas=(0.5, 0.999)
        )

        # Initialize loss function
        self.criterion = nn.BCELoss()
        self.logger = logging.getLogger(str(self))
        # Move entire model to device
        if device is not None:
            self.to(device)

    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Generate synthetic data for all tables"""
        return (
            self.main_gen(x),
            self.perf_gen(x),
            self.conv_gen(x)
        )

    def generate(self, num_samples: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Generate synthetic data for all tables"""
        try:
            # Generate random noise on correct device
            z = torch.randn(num_samples, self.noise_dim, device=self.device)

            # Generate data
            return (
                self.main_gen(z),
                self.perf_gen(z),
                self.conv_gen(z)
            )
        except Exception as e:
            self.logger.error(f"Error in generate: {str(e)}")
            raise RuntimeError(f"Error in generate: {str(e)}")

    def train_step(self, real_main: torch.Tensor, real_perf: torch.Tensor, real_conv: torch.Tensor) -> Tuple[float, float]:
        """Perform a single training step"""
        try:
            batch_size = real_main.size(0)

            # Move data to device
            real_main = real_main.to(self.device)
            real_perf = real_perf.to(self.device)
            real_conv = real_conv.to(self.device)

            # Train Discriminators
            self.optimizer_D.zero_grad()

            # Real data
            real_labels = torch.ones(batch_size, 1).to(self.device)
            main_real_loss = self.criterion(self.main_disc(real_main), real_labels)
            perf_real_loss = self.criterion(self.perf_disc(real_perf), real_labels)
            conv_real_loss = self.criterion(self.conv_disc(real_conv), real_labels)

            # Fake data
            z = torch.randn(batch_size, self.noise_dim).to(self.device)
            fake_main = self.main_gen(z)
            fake_perf = self.perf_gen(z)
            fake_conv = self.conv_gen(z)

            fake_labels = torch.zeros(batch_size, 1).to(self.device)
            main_fake_loss = self.criterion(self.main_disc(fake_main.detach()), fake_labels)
            perf_fake_loss = self.criterion(self.perf_disc(fake_perf.detach()), fake_labels)
            conv_fake_loss = self.criterion(self.conv_disc(fake_conv.detach()), fake_labels)

            # Total discriminator loss
            d_loss = (main_real_loss + perf_real_loss + conv_real_loss +
                     main_fake_loss + perf_fake_loss + conv_fake_loss) / 6
            d_loss.backward()
            self.optimizer_D.step()

            # Train Generators
            self.optimizer_G.zero_grad()
            main_g_loss = self.criterion(self.main_disc(fake_main), real_labels)
            perf_g_loss = self.criterion(self.perf_disc(fake_perf), real_labels)
            conv_g_loss = self.criterion(self.conv_disc(fake_conv), real_labels)
            g_loss = (main_g_loss + perf_g_loss + conv_g_loss) / 3
            g_loss.backward()
            self.optimizer_G.step()

            return d_loss.item(), g_loss.item()

        except Exception as e:
            self.logger.error(f"Error in train_step: {str(e)}")
            return float('nan'), float('nan')

    def train(self, dataloaders: torch.utils.data.DataLoader, epochs: int = 100) -> None:
        """Train all GANs simultaneously"""
        for epoch in range(epochs):
            d_losses = []
            g_losses = []

            for real_main, real_perf, real_conv in dataloaders:
                d_loss, g_loss = self.train_step(real_main, real_perf, real_conv)
                d_losses.append(d_loss)
                g_losses.append(g_loss)

            avg_d_loss = sum(d_losses) / len(d_losses)
            avg_g_loss = sum(g_losses) / len(g_losses)

            self.logger.info(f"Epoch {epoch+1}/{epochs} | D_loss: {avg_d_loss:.4f} | G_loss: {avg_g_loss:.4f}")

    def evaluate(self, real_dfs: List[pd.DataFrame], synthetic_dfs: List[pd.DataFrame]) -> Dict[str, Dict[str, float]]:
        """Evaluate quality of synthetic data"""
        results = {}
        for (real_df, synth_df), name in zip(zip(real_dfs, synthetic_dfs),
                                          ['main', 'performance', 'conversions']):
            results[name] = self._evaluate_table(real_df, synth_df)
        return results

    def _evaluate_table(self, real_df: pd.DataFrame, synth_df: pd.DataFrame) -> Dict[str, float]:
        """Evaluate a single table"""
        results = {}
        categorical_cols = [c for c in real_df.columns if real_df[c].dtype == 'object']
        numerical_cols = real_df.select_dtypes(include=['float64', 'int64']).columns

        for col in numerical_cols:
            if col in synth_df.columns:
                ks_stat, _ = ks_2samp(real_df[col], synth_df[col])
                results[f'{col}_KS'] = ks_stat

        for col in categorical_cols:
            if col in synth_df.columns:
                mi = mutual_info_score(real_df[col], synth_df[col])
                results[f'{col}_MI'] = mi

        return results

    def save(self, path: str) -> None:
        """Save entire model state"""
        torch.save({
            'main_gen_state': self.main_gen.state_dict(),
            'perf_gen_state': self.perf_gen.state_dict(),
            'conv_gen_state': self.conv_gen.state_dict(),
            'main_disc_state': self.main_disc.state_dict(),
            'perf_disc_state': self.perf_disc.state_dict(),
            'conv_disc_state': self.conv_disc.state_dict(),
            'noise_dim': self.noise_dim,
            'device': self.device
        }, path)

    def load(self, path: str) -> None:
        """Load model state"""
        try:
            checkpoint = torch.load(path)
            self.main_gen.load_state_dict(checkpoint['main_gen_state'])
            self.perf_gen.load_state_dict(checkpoint['perf_gen_state'])
            self.conv_gen.load_state_dict(checkpoint['conv_gen_state'])
            self.main_disc.load_state_dict(checkpoint['main_disc_state'])
            self.perf_disc.load_state_dict(checkpoint['perf_disc_state'])
            self.conv_disc.load_state_dict(checkpoint['conv_disc_state'])
            self.noise_dim = checkpoint['noise_dim']
            self.device = checkpoint['device']

            # Move all components to device
            self.main_gen.device = self.device
            self.perf_gen.device = self.device
            self.conv_gen.device = self.device
            self.main_disc.device = self.device
            self.perf_disc.device = self.device
            self.conv_disc.device = self.device

        except Exception as e:
            self.logger.error(f"Error loading model: {str(e)}")
            raise RuntimeError(f"Error loading model: {str(e)}")