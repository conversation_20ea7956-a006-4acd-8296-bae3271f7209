import logging
from typing import Tuple

import torch
import torch.nn as nn


class ConditionalGenerator(nn.Module):
    
    def __init__(self, media_dim: int, perf_dim: int, conv_dim: int, 
                 noise_dim: int = 100, hidden_dim: int = 256):
        super(ConditionalGenerator, self).__init__()
        
        self.media_dim = media_dim
        self.perf_dim = perf_dim
        self.conv_dim = conv_dim
        self.noise_dim = noise_dim
        
        # Shared encoder for media dimension input
        self.media_encoder = nn.Sequential(
            nn.Linear(media_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU()
        )
        
        # Performance generator
        self.perf_generator = nn.Sequential(
            nn.Linear(noise_dim + hidden_dim // 2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, perf_dim),
            nn.Tanh()
        )
        
        # Conversions generator
        self.conv_generator = nn.Sequential(
            nn.Linear(noise_dim + hidden_dim // 2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, conv_dim),
            nn.Tanh()
        )
        
    def forward(self, media_data: torch.Tensor, noise: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Generate performance and conversions data conditioned on media dimension
        
        Args:
            media_data: Media dimension input [batch_size, media_dim]
            noise: Random noise [batch_size, noise_dim]
            
        Returns:
            Tuple of (performance_data, conversions_data)
        """
        # Encode media dimension
        media_encoded = self.media_encoder(media_data)
        
        # Concatenate encoded media with noise
        combined_input = torch.cat([media_encoded, noise], dim=1)
        
        # Generate performance and conversions
        perf_data = self.perf_generator(combined_input)
        conv_data = self.conv_generator(combined_input)
        
        return perf_data, conv_data


class ConditionalDiscriminator(nn.Module):
    """
    Conditional Discriminator that evaluates generated performance/conversions
    given the media dimension context
    """
    
    def __init__(self, media_dim: int, perf_dim: int, conv_dim: int, hidden_dim: int = 256):
        super(ConditionalDiscriminator, self).__init__()
        
        self.media_dim = media_dim
        self.perf_dim = perf_dim
        self.conv_dim = conv_dim
        
        # Media encoder
        self.media_encoder = nn.Sequential(
            nn.Linear(media_dim, hidden_dim // 2),
            nn.LeakyReLU(0.2)
        )
        
        # Performance discriminator
        self.perf_discriminator = nn.Sequential(
            nn.Linear(perf_dim + hidden_dim // 2, hidden_dim),
            nn.LeakyReLU(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LeakyReLU(0.2),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()
        )
        
        # Conversions discriminator
        self.conv_discriminator = nn.Sequential(
            nn.Linear(conv_dim + hidden_dim // 2, hidden_dim),
            nn.LeakyReLU(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LeakyReLU(0.2),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()
        )
        
    def forward(self, media_data: torch.Tensor, perf_data: torch.Tensor, 
                conv_data: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Discriminate performance and conversions data given media context
        
        Args:
            media_data: Media dimension input [batch_size, media_dim]
            perf_data: Performance data [batch_size, perf_dim]
            conv_data: Conversions data [batch_size, conv_dim]
            
        Returns:
            Tuple of (perf_validity, conv_validity)
        """
        # Encode media dimension
        media_encoded = self.media_encoder(media_data)
        
        # Concatenate media context with each table
        perf_input = torch.cat([perf_data, media_encoded], dim=1)
        conv_input = torch.cat([conv_data, media_encoded], dim=1)
        
        # Discriminate each table
        perf_validity = self.perf_discriminator(perf_input)
        conv_validity = self.conv_discriminator(conv_input)
        
        return perf_validity, conv_validity


class ConditionalGAN(nn.Module):
    """
    Complete Conditional GAN system for media-conditioned generation
    """
    
    def __init__(self, media_dim: int, perf_dim: int, conv_dim: int, 
                 noise_dim: int = 100, device: str = 'cpu'):
        super(ConditionalGAN, self).__init__()
        
        self.logger = logging.getLogger("CONDITIONAL_GAN")
        self.device = device
        self.noise_dim = noise_dim
        
        # Initialize generator and discriminator
        self.generator = ConditionalGenerator(media_dim, perf_dim, conv_dim, noise_dim).to(device)
        self.discriminator = ConditionalDiscriminator(media_dim, perf_dim, conv_dim).to(device)
        
        self.logger.info(f"Conditional GAN initialized on {device}")
        self.logger.info(f"Media dim: {media_dim}, Perf dim: {perf_dim}, Conv dim: {conv_dim}")
        
    def generate(self, media_data: torch.Tensor, num_samples: int = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Generate performance and conversions data conditioned on media dimension
        
        Args:
            media_data: Media dimension input [batch_size, media_dim]
            num_samples: Number of samples (if None, uses media_data batch size)
            
        Returns:
            Tuple of (performance_data, conversions_data)
        """
        # Ensure media_data is on the correct device
        media_data = media_data.to(self.device)
        
        # Determine batch size
        if num_samples is None:
            batch_size = media_data.shape[0]
        else:
            batch_size = num_samples
        
        # Log dimensions for debugging
        self.logger.info(f"Generate called with media_data shape: {media_data.shape}, requested samples: {num_samples}")
        self.logger.info(f"Expected media_dim: {self.generator.media_dim}, using batch_size: {batch_size}")
        
        # Ensure media_data has the correct feature dimension
        if media_data.shape[1] != self.generator.media_dim:
            self.logger.warning(f"Media data has {media_data.shape[1]} features but model expects {self.generator.media_dim}")
            if media_data.shape[1] < self.generator.media_dim:
                # Pad with zeros if we have fewer features than expected
                padding = torch.zeros(media_data.shape[0], self.generator.media_dim - media_data.shape[1], device=self.device)
                media_data = torch.cat([media_data, padding], dim=1)
            else:
                # Truncate if we have more features than expected
                media_data = media_data[:, :self.generator.media_dim]
            self.logger.info(f"Adjusted media_data shape: {media_data.shape}")
            
        # Ensure media_data has the correct batch size
        if media_data.shape[0] != batch_size:
            self.logger.info(f"Adjusting media_data batch size from {media_data.shape[0]} to {batch_size}")
            # Repeat or sample media_data to match batch_size
            if media_data.shape[0] < batch_size:
                repeat_factor = (batch_size + media_data.shape[0] - 1) // media_data.shape[0]
                media_data = media_data.repeat(repeat_factor, 1)[:batch_size]
            else:
                media_data = media_data[:batch_size]
        
        # Generate random noise with matching batch size
        noise = torch.randn(batch_size, self.noise_dim, device=self.device)
        self.logger.info(f"Generated noise tensor with shape: {noise.shape}")
        
        # Generate data
        try:
            with torch.no_grad():
                perf_data, conv_data = self.generator(media_data, noise)
            self.logger.info(f"Successfully generated data with shapes: perf={perf_data.shape}, conv={conv_data.shape}")
            return perf_data, conv_data
        except Exception as e:
            self.logger.error(f"Error during generation: {str(e)}")
            self.logger.error(f"Media shape: {media_data.shape}, Noise shape: {noise.shape}")
            raise
        
    def forward(self, media_data: torch.Tensor, noise: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass for training"""
        return self.generator(media_data, noise)
