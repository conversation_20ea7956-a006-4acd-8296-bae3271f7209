import concurrent.futures
import logging
from typing import Any, Dict, Optional

import numpy as np
import pandas as pd


class StatisticsEvaluator:
    """
    Specialized evaluator for statistical comparisons between real and synthetic data.
    """

    def __str__(self) -> str:
        return "STATISTICS-EVALUATOR"

    def __init__(self, synthetic_data: Dict[str, Any], real_data: Optional[Dict[str, Any]]):
        """
        Initialize statistics evaluator

        Args:
            synthetic_data: Dictionary containing synthetic data tables
            real_data: Dictionary containing real data tables for comparison
        """
        self.logger = logging.getLogger(str(self))
        self.synthetic_data = synthetic_data
        self.real_data = real_data

    def compare_statistics(self):
        """Compare basic statistics between real and synthetic data using parallel processing"""
        comparison = {}

        def process_table_statistics(table):
            """Process statistics for a single table"""
            if not self.real_data or table not in self.real_data or table not in self.synthetic_data:
                return table, {}

            real_df = self.real_data[table]
            synth_df = self.synthetic_data[table]

            table_comparison = {}

            # Compare numerical columns
            num_cols = real_df.select_dtypes(include=np.number).columns
            common_cols = set(num_cols) & set(synth_df.columns)

            for col in common_cols:
                if 'date' not in col.lower():
                    # Attempt to coerce to numeric, log and skip if not possible
                    real_col = pd.to_numeric(real_df[col], errors='coerce')
                    synth_col = pd.to_numeric(synth_df[col], errors='coerce')
                    real_nan_ratio = real_col.isna().mean()
                    synth_nan_ratio = synth_col.isna().mean()
                    if real_nan_ratio > 0.5 or synth_nan_ratio > 0.5:
                        self.logger.warning(f"Skipping column '{col}' in table '{table}' due to non-numeric values (real_nan_ratio={real_nan_ratio:.2f}, synth_nan_ratio={synth_nan_ratio:.2f})")
                        continue
                    real_stats = {
                        'mean': real_col.mean(),
                        'std': real_col.std(),
                        'min': real_col.min(),
                        'max': real_col.max(),
                        'median': real_col.median()
                    }
                    synth_stats = {
                        'mean': synth_col.mean(),
                        'std': synth_col.std(),
                        'min': synth_col.min(),
                        'max': synth_col.max(),
                        'median': synth_col.median()
                    }

                    # Calculate differences
                    differences = {}
                    for stat in real_stats:
                        if real_stats[stat] != 0:
                            differences[f'{stat}_diff_pct'] = abs(
                                (synth_stats[stat] - real_stats[stat]) / real_stats[stat] * 100
                            )
                        else:
                            differences[f'{stat}_diff_abs'] = abs(synth_stats[stat] - real_stats[stat])

                    table_comparison[col] = {
                        'real': real_stats,
                        'synthetic': synth_stats,
                        'differences': differences
                    }

            return table, table_comparison

        # Process tables in parallel
        self.logger.info("Running statistical comparison in parallel...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            future_to_table = {
                executor.submit(process_table_statistics, table): table
                for table in ['main', 'performance', 'conversions']
            }

            for future in concurrent.futures.as_completed(future_to_table):
                table, table_comparison = future.result()
                comparison[table] = table_comparison

        return comparison

    def calculate_statistical_summary(self, comparison_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate overall statistical summary from comparison results

        Args:
            comparison_results: Results from compare_statistics()

        Returns:
            Dict containing summary statistics
        """
        summary = {
            'total_columns_compared': 0,
            'average_mean_difference_pct': 0,
            'average_std_difference_pct': 0,
            'columns_with_high_difference': [],
            'overall_statistical_score': 0
        }

        all_mean_diffs = []
        all_std_diffs = []
        high_diff_threshold = 20.0  # 20% difference threshold

        for table, table_data in comparison_results.items():
            for col, col_data in table_data.items():
                if 'differences' in col_data:
                    summary['total_columns_compared'] += 1
                    
                    # Extract mean and std differences
                    if 'mean_diff_pct' in col_data['differences']:
                        mean_diff = col_data['differences']['mean_diff_pct']
                        all_mean_diffs.append(mean_diff)
                        
                        if mean_diff > high_diff_threshold:
                            summary['columns_with_high_difference'].append(f"{table}.{col}")
                    
                    if 'std_diff_pct' in col_data['differences']:
                        std_diff = col_data['differences']['std_diff_pct']
                        all_std_diffs.append(std_diff)

        # Calculate averages
        if all_mean_diffs:
            summary['average_mean_difference_pct'] = np.mean(all_mean_diffs)
        if all_std_diffs:
            summary['average_std_difference_pct'] = np.mean(all_std_diffs)

        # Calculate overall score (lower differences = higher score)
        if all_mean_diffs and all_std_diffs:
            avg_diff = (summary['average_mean_difference_pct'] + summary['average_std_difference_pct']) / 2
            # Score from 0-100, where 0% difference = 100 score, 50% difference = 0 score
            summary['overall_statistical_score'] = max(0, 100 - (avg_diff * 2))

        return summary

    def get_column_statistics(self, table_name: str, column_name: str) -> Dict[str, Any]:
        """
        Get detailed statistics for a specific column

        Args:
            table_name: Name of the table
            column_name: Name of the column

        Returns:
            Dict containing detailed column statistics
        """
        if table_name not in self.synthetic_data:
            return {}

        synth_df = self.synthetic_data[table_name]
        if column_name not in synth_df.columns:
            return {}

        stats = {
            'synthetic': {
                'count': synth_df[column_name].count(),
                'null_count': synth_df[column_name].isnull().sum(),
                'unique_count': synth_df[column_name].nunique(),
                'data_type': str(synth_df[column_name].dtype)
            }
        }

        # Add numerical statistics if applicable
        if synth_df[column_name].dtype in ['int64', 'float64']:
            stats['synthetic'].update({
                'mean': synth_df[column_name].mean(),
                'std': synth_df[column_name].std(),
                'min': synth_df[column_name].min(),
                'max': synth_df[column_name].max(),
                'median': synth_df[column_name].median(),
                'q25': synth_df[column_name].quantile(0.25),
                'q75': synth_df[column_name].quantile(0.75)
            })

        # Add real data statistics if available
        if self.real_data and table_name in self.real_data:
            real_df = self.real_data[table_name]
            if column_name in real_df.columns:
                stats['real'] = {
                    'count': real_df[column_name].count(),
                    'null_count': real_df[column_name].isnull().sum(),
                    'unique_count': real_df[column_name].nunique(),
                    'data_type': str(real_df[column_name].dtype)
                }

                if real_df[column_name].dtype in ['int64', 'float64']:
                    stats['real'].update({
                        'mean': real_df[column_name].mean(),
                        'std': real_df[column_name].std(),
                        'min': real_df[column_name].min(),
                        'max': real_df[column_name].max(),
                        'median': real_df[column_name].median(),
                        'q25': real_df[column_name].quantile(0.25),
                        'q75': real_df[column_name].quantile(0.75)
                    })

        return stats
