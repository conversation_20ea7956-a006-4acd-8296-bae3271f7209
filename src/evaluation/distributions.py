import concurrent.futures
import logging
from typing import Any, Dict, Optional

import numpy as np
import pandas as pd
from scipy.stats import ks_2samp, wasserstein_distance

try:
    from src.config import settings
    SETTINGS_AVAILABLE = True
except (ImportError, KeyError):
    SETTINGS_AVAILABLE = False


class DistributionEvaluator:
    """
    Specialized evaluator for distribution comparisons using statistical tests.
    """

    def __str__(self) -> str:
        return "DISTRIBUTION-EVALUATOR"

    def __init__(self, synthetic_data: Dict[str, Any], real_data: Optional[Dict[str, Any]]):
        """
        Initialize distribution evaluator

        Args:
            synthetic_data: Dictionary containing synthetic data tables
            real_data: Dictionary containing real data tables for comparison
        """
        self.logger = logging.getLogger(str(self))
        self.synthetic_data = synthetic_data
        self.real_data = real_data

    def compare_distributions(self):
        """Compare distributions using statistical tests with parallel processing"""
        comparison = {}

        def process_table_distributions(table):
            """Process distribution comparisons for a single table"""
            if not self.real_data or table not in self.real_data or table not in self.synthetic_data:
                return table, {}

            real_df = self.real_data[table]
            synth_df = self.synthetic_data[table]

            table_comparison = {}

            # Compare numerical columns using KS test
            num_cols = real_df.select_dtypes(include=np.number).columns
            common_cols = set(num_cols) & set(synth_df.columns)

            for col in common_cols:
                if 'date' not in col.lower():
                    try:
                        # Validate data types and clean data
                        real_values = real_df[col].dropna()
                        synth_values = synth_df[col].dropna()

                        # NUCLEAR FIX: Remove any string values longer than 50 chars before numeric conversion
                        if real_values.dtype == 'object':
                            real_values = real_values[real_values.astype(str).str.len() <= 50]
                        if synth_values.dtype == 'object':
                            synth_values = synth_values[synth_values.astype(str).str.len() <= 50]

                        # Ensure both columns are numeric and compatible
                        real_values = pd.to_numeric(real_values, errors='coerce').dropna()
                        synth_values = pd.to_numeric(synth_values, errors='coerce').dropna()

                        # Skip if insufficient data after cleaning
                        if len(real_values) < 10 or len(synth_values) < 10:
                            self.logger.info(f"Skipping {table}.{col} - insufficient numeric data after cleaning")
                            continue

                        # Kolmogorov-Smirnov test
                        ks_stat, ks_p_value = ks_2samp(real_values, synth_values)

                        # Wasserstein distance (Earth Mover's Distance)
                        wasserstein_dist = wasserstein_distance(real_values, synth_values)

                        # Default KS test threshold
                        ks_threshold = 0.05
                        if SETTINGS_AVAILABLE:
                            try:
                                ks_threshold = settings.EVALUATION_PARAMS.get('ks_test_threshold', 0.05)
                            except (KeyError, AttributeError):
                                pass  # Use default

                        table_comparison[col] = {
                            'ks_statistic': ks_stat,
                            'ks_p_value': ks_p_value,
                            'ks_similar': ks_p_value > ks_threshold,
                            'wasserstein_distance': wasserstein_dist
                        }
                    except Exception as e:
                        self.logger.warning(f"Could not compare distributions for {table}.{col}: {str(e)}")
                        continue

            # Compare categorical columns using entropy analysis
            cat_cols = real_df.select_dtypes(include='object').columns
            common_cat_cols = set(cat_cols) & set(synth_df.columns)

            for col in common_cat_cols:
                if 'date' not in col.lower():
                    try:
                        # Clean and validate categorical data
                        real_values = real_df[col].dropna().astype(str)
                        synth_values = synth_df[col].dropna().astype(str)

                        # NUCLEAR FIX: Filter out ANY values longer than 50 characters
                        real_values = real_values[real_values.str.len() <= 50]
                        synth_values = synth_values[synth_values.str.len() <= 50]

                        # Skip if insufficient data after cleaning
                        if len(real_values) < 5 or len(synth_values) < 5:
                            self.logger.info(f"Skipping {table}.{col} - insufficient clean data after filtering")
                            continue

                        # Get value counts for both datasets
                        real_counts = real_values.value_counts(normalize=True)
                        synth_counts = synth_values.value_counts(normalize=True)

                        # Calculate entropy for diversity measure
                        real_entropy = -np.sum(real_counts * np.log2(real_counts + 1e-10))
                        synth_entropy = -np.sum(synth_counts * np.log2(synth_counts + 1e-10))

                        table_comparison[col] = {
                            'real_entropy': real_entropy,
                            'synthetic_entropy': synth_entropy,
                            'entropy_difference': abs(real_entropy - synth_entropy),
                            'real_unique_values': len(real_counts),
                            'synthetic_unique_values': len(synth_counts)
                        }
                    except Exception as e:
                        self.logger.warning(f"Could not compare categorical distributions for {table}.{col}: {str(e)}")
                        continue

            return table, table_comparison

        # Process tables in parallel
        self.logger.info("Running distribution comparison in parallel...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            future_to_table = {
                executor.submit(process_table_distributions, table): table
                for table in ['main', 'performance', 'conversions']
            }

            for future in concurrent.futures.as_completed(future_to_table):
                table, table_comparison = future.result()
                comparison[table] = table_comparison

        return comparison

    def calculate_distribution_summary(self, comparison_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate overall distribution quality summary

        Args:
            comparison_results: Results from compare_distributions()

        Returns:
            Dict containing distribution summary
        """
        summary = {
            'total_numerical_columns': 0,
            'total_categorical_columns': 0,
            'similar_distributions_count': 0,
            'distribution_similarity_score': 0,
            'average_ks_statistic': 0,
            'average_wasserstein_distance': 0,
            'average_entropy_difference': 0
        }

        ks_stats = []
        wasserstein_dists = []
        entropy_diffs = []
        similar_count = 0

        for table, table_data in comparison_results.items():
            for col, col_data in table_data.items():
                if 'ks_statistic' in col_data:
                    summary['total_numerical_columns'] += 1
                    ks_stats.append(col_data['ks_statistic'])
                    wasserstein_dists.append(col_data['wasserstein_distance'])

                    if col_data['ks_similar']:
                        similar_count += 1

                elif 'entropy_difference' in col_data:
                    summary['total_categorical_columns'] += 1
                    entropy_diffs.append(col_data['entropy_difference'])

        # Calculate averages
        if ks_stats:
            summary['average_ks_statistic'] = np.mean(ks_stats)
        if wasserstein_dists:
            summary['average_wasserstein_distance'] = np.mean(wasserstein_dists)
        if entropy_diffs:
            summary['average_entropy_difference'] = np.mean(entropy_diffs)

        summary['similar_distributions_count'] = similar_count

        # Calculate overall similarity score
        total_columns = summary['total_numerical_columns'] + summary['total_categorical_columns']
        if total_columns > 0:
            # For numerical: use KS test similarity
            # For categorical: use entropy similarity (lower difference = higher similarity)
            numerical_score = similar_count / summary['total_numerical_columns'] if summary['total_numerical_columns'] > 0 else 1.0

            categorical_score = 1.0
            if summary['total_categorical_columns'] > 0 and entropy_diffs:
                # Normalize entropy differences to 0-1 scale (assuming max reasonable difference is 5)
                avg_entropy_diff = summary['average_entropy_difference']
                categorical_score = max(0, 1 - (avg_entropy_diff / 5.0))

            # Weighted average based on column counts
            if summary['total_numerical_columns'] > 0 and summary['total_categorical_columns'] > 0:
                num_weight = summary['total_numerical_columns'] / total_columns
                cat_weight = summary['total_categorical_columns'] / total_columns
                summary['distribution_similarity_score'] = (numerical_score * num_weight + categorical_score * cat_weight) * 100
            elif summary['total_numerical_columns'] > 0:
                summary['distribution_similarity_score'] = numerical_score * 100
            else:
                summary['distribution_similarity_score'] = categorical_score * 100

        return summary

    def analyze_column_distribution(self, table_name: str, column_name: str) -> Dict[str, Any]:
        """
        Analyze distribution for a specific column

        Args:
            table_name: Name of the table
            column_name: Name of the column

        Returns:
            Dict containing distribution analysis
        """
        if table_name not in self.synthetic_data:
            return {}

        synth_df = self.synthetic_data[table_name]
        if column_name not in synth_df.columns:
            return {}

        analysis = {
            'column_name': column_name,
            'table_name': table_name,
            'data_type': str(synth_df[column_name].dtype)
        }

        # Numerical distribution analysis
        if synth_df[column_name].dtype in ['int64', 'float64']:
            synth_values = synth_df[column_name].dropna()

            analysis['synthetic'] = {
                'skewness': synth_values.skew(),
                'kurtosis': synth_values.kurtosis(),
                'value_range': synth_values.max() - synth_values.min(),
                'percentiles': {
                    '5th': synth_values.quantile(0.05),
                    '25th': synth_values.quantile(0.25),
                    '50th': synth_values.quantile(0.50),
                    '75th': synth_values.quantile(0.75),
                    '95th': synth_values.quantile(0.95)
                }
            }

            # Add real data comparison if available
            if self.real_data and table_name in self.real_data:
                real_df = self.real_data[table_name]
                if column_name in real_df.columns:
                    real_values = real_df[column_name].dropna()

                    analysis['real'] = {
                        'skewness': real_values.skew(),
                        'kurtosis': real_values.kurtosis(),
                        'value_range': real_values.max() - real_values.min(),
                        'percentiles': {
                            '5th': real_values.quantile(0.05),
                            '25th': real_values.quantile(0.25),
                            '50th': real_values.quantile(0.50),
                            '75th': real_values.quantile(0.75),
                            '95th': real_values.quantile(0.95)
                        }
                    }

                    # Statistical tests
                    try:
                        ks_stat, ks_p = ks_2samp(real_values, synth_values)
                        wasserstein_dist = wasserstein_distance(real_values, synth_values)

                        analysis['comparison'] = {
                            'ks_statistic': ks_stat,
                            'ks_p_value': ks_p,
                            'wasserstein_distance': wasserstein_dist,
                            'distributions_similar': ks_p > 0.05
                        }
                    except Exception as e:
                        self.logger.warning(f"Could not perform statistical tests: {str(e)}")

        # Categorical distribution analysis
        elif synth_df[column_name].dtype == 'object':
            synth_counts = synth_df[column_name].value_counts(normalize=True)

            analysis['synthetic'] = {
                'unique_values': len(synth_counts),
                'entropy': -np.sum(synth_counts * np.log2(synth_counts + 1e-10)),
                'top_values': synth_counts.head(10).to_dict()
            }

            # Add real data comparison if available
            if self.real_data and table_name in self.real_data:
                real_df = self.real_data[table_name]
                if column_name in real_df.columns:
                    real_counts = real_df[column_name].value_counts(normalize=True)

                    analysis['real'] = {
                        'unique_values': len(real_counts),
                        'entropy': -np.sum(real_counts * np.log2(real_counts + 1e-10)),
                        'top_values': real_counts.head(10).to_dict()
                    }

                    analysis['comparison'] = {
                        'entropy_difference': abs(analysis['real']['entropy'] - analysis['synthetic']['entropy']),
                        'unique_value_difference': abs(analysis['real']['unique_values'] - analysis['synthetic']['unique_values'])
                    }

        return analysis
