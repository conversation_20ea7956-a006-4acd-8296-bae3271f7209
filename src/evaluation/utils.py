from typing import Any, Dict, List, Optional, <PERSON><PERSON>

import numpy as np
import pandas as pd


def sample_dataframe(df: pd.DataFrame, sample_size: int, random_state: int = 42) -> pd.DataFrame:
    """
    Sample a dataframe to a specified size for fast evaluation

    Args:
        df: DataFrame to sample
        sample_size: Target sample size
        random_state: Random state for reproducibility

    Returns:
        Sampled DataFrame
    """
    if len(df) <= sample_size:
        return df

    return df.sample(n=sample_size, random_state=random_state)


def calculate_percentage_difference(value1: float, value2: float) -> float:
    """
    Calculate percentage difference between two values

    Args:
        value1: First value (baseline)
        value2: Second value (comparison)

    Returns:
        Percentage difference
    """
    if value1 == 0:
        return float('inf') if value2 != 0 else 0.0

    return abs((value2 - value1) / value1) * 100


def normalize_column_names(df: pd.DataFrame) -> pd.DataFrame:
    """
    Normalize column names for consistent comparison

    Args:
        df: DataFrame with columns to normalize

    Returns:
        DataFrame with normalized column names
    """
    df_copy = df.copy()
    df_copy.columns = df_copy.columns.str.lower().str.replace(' ', '_').str.replace('-', '_')
    return df_copy


def get_common_columns(df1: pd.DataFrame, df2: pd.DataFrame,
                      column_types: Optional[List[str]] = None) -> List[str]:
    """
    Get common columns between two dataframes, optionally filtered by type

    Args:
        df1: First DataFrame
        df2: Second DataFrame
        column_types: List of column types to include (e.g., ['number', 'object'])

    Returns:
        List of common column names
    """
    common_cols = list(set(df1.columns) & set(df2.columns))

    if column_types:
        filtered_cols = []
        for col in common_cols:
            if any(df1[col].dtype.name.startswith(ctype) or
                   str(df1[col].dtype) in column_types for ctype in column_types):
                filtered_cols.append(col)
        return filtered_cols

    return common_cols


def get_numerical_columns(df: pd.DataFrame, exclude_patterns: Optional[List[str]] = None) -> List[str]:
    """
    Get numerical columns from a dataframe, excluding specified patterns

    Args:
        df: DataFrame to analyze
        exclude_patterns: List of patterns to exclude (e.g., ['date', 'id'])

    Returns:
        List of numerical column names
    """
    num_cols = df.select_dtypes(include=np.number).columns.tolist()

    if exclude_patterns:
        filtered_cols = []
        for col in num_cols:
            if not any(pattern.lower() in col.lower() for pattern in exclude_patterns):
                filtered_cols.append(col)
        return filtered_cols

    return num_cols


def get_categorical_columns(df: pd.DataFrame, exclude_patterns: Optional[List[str]] = None) -> List[str]:
    """
    Get categorical columns from a dataframe, excluding specified patterns

    Args:
        df: DataFrame to analyze
        exclude_patterns: List of patterns to exclude (e.g., ['date'])

    Returns:
        List of categorical column names
    """
    cat_cols = df.select_dtypes(include='object').columns.tolist()

    if exclude_patterns:
        filtered_cols = []
        for col in cat_cols:
            if not any(pattern.lower() in col.lower() for pattern in exclude_patterns):
                filtered_cols.append(col)
        return filtered_cols

    return cat_cols


def calculate_entropy(series: pd.Series) -> float:
    """
    Calculate entropy of a categorical series

    Args:
        series: Pandas series with categorical data

    Returns:
        Entropy value
    """
    value_counts = series.value_counts(normalize=True)
    return -np.sum(value_counts * np.log2(value_counts + 1e-10))


def detect_outliers_iqr(series: pd.Series, multiplier: float = 1.5) -> Tuple[pd.Series, Dict[str, float]]:
    """
    Detect outliers using IQR method

    Args:
        series: Numerical series to analyze
        multiplier: IQR multiplier for outlier detection

    Returns:
        Tuple of (outlier_mask, outlier_stats)
    """
    Q1 = series.quantile(0.25)
    Q3 = series.quantile(0.75)
    IQR = Q3 - Q1

    lower_bound = Q1 - multiplier * IQR
    upper_bound = Q3 + multiplier * IQR

    outlier_mask = (series < lower_bound) | (series > upper_bound)

    outlier_stats = {
        'lower_bound': lower_bound,
        'upper_bound': upper_bound,
        'outlier_count': outlier_mask.sum(),
        'outlier_percentage': (outlier_mask.sum() / len(series)) * 100
    }

    return outlier_mask, outlier_stats


def calculate_data_quality_score(df: pd.DataFrame) -> Dict[str, float]:
    """
    Calculate overall data quality score for a dataframe

    Args:
        df: DataFrame to analyze

    Returns:
        Dictionary with quality metrics
    """
    total_cells = df.shape[0] * df.shape[1]
    null_cells = df.isnull().sum().sum()

    quality_metrics = {
        'completeness_score': ((total_cells - null_cells) / total_cells) * 100 if total_cells > 0 else 0,
        'null_percentage': (null_cells / total_cells) * 100 if total_cells > 0 else 0,
        'total_rows': df.shape[0],
        'total_columns': df.shape[1],
        'duplicate_rows': df.duplicated().sum(),
        'duplicate_percentage': (df.duplicated().sum() / len(df)) * 100 if len(df) > 0 else 0
    }

    # Calculate column-specific quality
    column_quality = {}
    for col in df.columns:
        col_quality = {
            'null_count': df[col].isnull().sum(),
            'null_percentage': (df[col].isnull().sum() / len(df)) * 100 if len(df) > 0 else 0,
            'unique_count': df[col].nunique(),
            'unique_percentage': (df[col].nunique() / len(df)) * 100 if len(df) > 0 else 0
        }

        # Add type-specific metrics
        if df[col].dtype in ['int64', 'float64']:
            col_quality.update({
                'zero_count': (df[col] == 0).sum(),
                'negative_count': (df[col] < 0).sum(),
                'infinite_count': np.isinf(df[col]).sum()
            })

        column_quality[col] = col_quality

    quality_metrics['column_quality'] = column_quality

    return quality_metrics


def compare_data_quality(df1: pd.DataFrame, df2: pd.DataFrame,
                        df1_name: str = "Dataset 1", df2_name: str = "Dataset 2") -> Dict[str, Any]:
    """
    Compare data quality between two dataframes

    Args:
        df1: First DataFrame
        df2: Second DataFrame
        df1_name: Name for first dataset
        df2_name: Name for second dataset

    Returns:
        Dictionary with quality comparison
    """
    quality1 = calculate_data_quality_score(df1)
    quality2 = calculate_data_quality_score(df2)

    comparison = {
        df1_name: quality1,
        df2_name: quality2,
        'differences': {
            'completeness_diff': quality2['completeness_score'] - quality1['completeness_score'],
            'null_percentage_diff': quality2['null_percentage'] - quality1['null_percentage'],
            'duplicate_percentage_diff': quality2['duplicate_percentage'] - quality1['duplicate_percentage'],
            'row_count_diff': quality2['total_rows'] - quality1['total_rows'],
            'column_count_diff': quality2['total_columns'] - quality1['total_columns']
        }
    }

    return comparison


def validate_evaluation_inputs(synthetic_data: Dict[str, Any],
                             real_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Validate inputs for evaluation process

    Args:
        synthetic_data: Dictionary containing synthetic data tables
        real_data: Optional dictionary containing real data tables

    Returns:
        Dictionary with validation results
    """
    validation_results = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'summary': {}
    }

    # Check synthetic data
    if not synthetic_data:
        validation_results['valid'] = False
        validation_results['errors'].append("Synthetic data is empty or None")
        return validation_results

    required_tables = ['main', 'performance', 'conversions']
    available_tables = list(synthetic_data.keys())

    validation_results['summary']['available_synthetic_tables'] = available_tables

    for table in required_tables:
        if table not in synthetic_data:
            validation_results['warnings'].append(f"Missing synthetic table: {table}")
        elif synthetic_data[table].empty:
            validation_results['warnings'].append(f"Synthetic table {table} is empty")
        else:
            validation_results['summary'][f'synthetic_{table}_shape'] = synthetic_data[table].shape

    # Check real data if provided
    if real_data:
        available_real_tables = list(real_data.keys())
        validation_results['summary']['available_real_tables'] = available_real_tables

        for table in required_tables:
            if table not in real_data:
                validation_results['warnings'].append(f"Missing real data table: {table}")
            elif real_data[table].empty:
                validation_results['warnings'].append(f"Real data table {table} is empty")
            else:
                validation_results['summary'][f'real_{table}_shape'] = real_data[table].shape

    # Check for common columns between real and synthetic data
    if real_data:
        for table in available_tables:
            if table in real_data:
                common_cols = get_common_columns(synthetic_data[table], real_data[table])
                validation_results['summary'][f'{table}_common_columns'] = len(common_cols)

                if len(common_cols) == 0:
                    validation_results['errors'].append(f"No common columns between real and synthetic {table} tables")
                    validation_results['valid'] = False

    return validation_results


def create_evaluation_config(fast_mode: bool = True,
                           sample_size: Optional[int] = None,
                           skip_visualizations: bool = None) -> Dict[str, Any]:
    """
    Create evaluation configuration based on settings and parameters

    Args:
        fast_mode: Whether to use fast mode
        sample_size: Custom sample size (overrides settings)
        skip_visualizations: Whether to skip visualizations (overrides fast_mode)

    Returns:
        Dictionary with evaluation configuration
    """
    config = {
        'fast_mode': fast_mode,
        'sample_size': sample_size or 10000,  # Default sample size
        'random_state': 42,  # Default random state
        'ks_test_threshold': 0.05,  # Default KS test threshold
        'acceptable_difference_threshold': 10.0,  # Default acceptable difference
        'skip_visualizations': skip_visualizations if skip_visualizations is not None else fast_mode,
        'parallel_processing': True,
        'max_workers': 3
    }

    return config
