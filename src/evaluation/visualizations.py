import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
import os
from typing import Dict, Any, Optional


class VisualizationGenerator:
    """
    Specialized generator for evaluation visualizations and plots.
    """

    def __str__(self) -> str:
        return "VISUALIZATION-GENERATOR"

    def __init__(self, synthetic_data: Dict[str, Any], real_data: Optional[Dict[str, Any]], fast_mode: bool = False):
        """
        Initialize visualization generator

        Args:
            synthetic_data: Dictionary containing synthetic data tables
            real_data: Dictionary containing real data tables for comparison
            fast_mode: Whether to skip visualization generation
        """
        self.logger = logging.getLogger(str(self))
        self.synthetic_data = synthetic_data
        self.real_data = real_data
        self.fast_mode = fast_mode

        # Set matplotlib style
        plt.style.use('default')
        sns.set_palette("husl")

    def generate_comparison_visuals(self, output_dir: str):
        """Generate comprehensive comparison visualizations"""
        if self.fast_mode:
            self.logger.info("Fast mode: Skipping visualization generation")
            return

        if not self.real_data:
            self.logger.warning("No real data available for comparison visualizations")
            return

        self.logger.info("Generating comparison visualizations...")

        try:
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)

            # Generate distribution comparisons
            self._generate_distribution_plots(output_dir)

            # Generate statistical comparison plots
            self._generate_statistical_plots(output_dir)

            # Generate correlation heatmaps
            self._generate_correlation_plots(output_dir)

            # Generate summary dashboard
            self._generate_summary_dashboard(output_dir)

            self.logger.info(f"Visualizations saved to {output_dir}")

        except Exception as e:
            self.logger.error(f"Error generating visualizations: {str(e)}")

    def _generate_distribution_plots(self, output_dir: str):
        """Generate distribution comparison plots"""
        for table_name in ['main', 'performance', 'conversions']:
            if table_name not in self.synthetic_data or table_name not in self.real_data:
                continue

            real_df = self.real_data[table_name]
            synth_df = self.synthetic_data[table_name]

            # Get numerical columns
            num_cols = real_df.select_dtypes(include=np.number).columns
            common_cols = list(set(num_cols) & set(synth_df.columns))

            if not common_cols:
                continue

            # Create subplots for numerical columns
            n_cols = min(4, len(common_cols))
            n_rows = (len(common_cols) + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(5*n_cols, 4*n_rows))
            if n_rows == 1 and n_cols == 1:
                axes = [axes]
            elif n_rows == 1:
                axes = axes
            else:
                axes = axes.flatten()

            for i, col in enumerate(common_cols[:n_rows*n_cols]):
                if i >= len(axes):
                    break

                ax = axes[i]

                # Plot distributions
                try:
                    real_values = real_df[col].dropna()
                    synth_values = synth_df[col].dropna()

                    if len(real_values) > 0 and len(synth_values) > 0:
                        ax.hist(real_values, bins=30, alpha=0.7, label='Real', density=True)
                        ax.hist(synth_values, bins=30, alpha=0.7, label='Synthetic', density=True)
                        ax.set_title(f'{col} Distribution')
                        ax.set_xlabel(col)
                        ax.set_ylabel('Density')
                        ax.legend()
                        ax.grid(True, alpha=0.3)

                except Exception as e:
                    self.logger.warning(f"Could not plot distribution for {col}: {str(e)}")
                    ax.text(0.5, 0.5, f'Error plotting {col}', ha='center', va='center', transform=ax.transAxes)

            # Hide empty subplots
            for i in range(len(common_cols), len(axes)):
                axes[i].set_visible(False)

            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'{table_name}_distributions.png'), dpi=300, bbox_inches='tight')
            plt.close()

    def _generate_statistical_plots(self, output_dir: str):
        """Generate statistical comparison plots"""
        for table_name in ['main', 'performance', 'conversions']:
            if table_name not in self.synthetic_data or table_name not in self.real_data:
                continue

            real_df = self.real_data[table_name]
            synth_df = self.synthetic_data[table_name]

            # Get numerical columns
            num_cols = real_df.select_dtypes(include=np.number).columns
            common_cols = list(set(num_cols) & set(synth_df.columns))

            if len(common_cols) < 2:
                continue

            # Create comparison plots
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))

            # Mean comparison
            real_means = [real_df[col].mean() for col in common_cols]
            synth_means = [synth_df[col].mean() for col in common_cols]

            axes[0, 0].scatter(real_means, synth_means, alpha=0.7)
            axes[0, 0].plot([min(real_means), max(real_means)], [min(real_means), max(real_means)], 'r--', alpha=0.8)
            axes[0, 0].set_xlabel('Real Data Means')
            axes[0, 0].set_ylabel('Synthetic Data Means')
            axes[0, 0].set_title('Mean Comparison')
            axes[0, 0].grid(True, alpha=0.3)

            # Standard deviation comparison
            real_stds = [real_df[col].std() for col in common_cols]
            synth_stds = [synth_df[col].std() for col in common_cols]

            axes[0, 1].scatter(real_stds, synth_stds, alpha=0.7)
            axes[0, 1].plot([min(real_stds), max(real_stds)], [min(real_stds), max(real_stds)], 'r--', alpha=0.8)
            axes[0, 1].set_xlabel('Real Data Std Dev')
            axes[0, 1].set_ylabel('Synthetic Data Std Dev')
            axes[0, 1].set_title('Standard Deviation Comparison')
            axes[0, 1].grid(True, alpha=0.3)

            # Min-Max comparison
            real_ranges = [real_df[col].max() - real_df[col].min() for col in common_cols]
            synth_ranges = [synth_df[col].max() - synth_df[col].min() for col in common_cols]

            axes[1, 0].scatter(real_ranges, synth_ranges, alpha=0.7)
            axes[1, 0].plot([min(real_ranges), max(real_ranges)], [min(real_ranges), max(real_ranges)], 'r--', alpha=0.8)
            axes[1, 0].set_xlabel('Real Data Range')
            axes[1, 0].set_ylabel('Synthetic Data Range')
            axes[1, 0].set_title('Range Comparison')
            axes[1, 0].grid(True, alpha=0.3)

            # Correlation comparison (if enough columns)
            if len(common_cols) >= 3:
                real_corr = real_df[common_cols].corr().values[np.triu_indices_from(real_df[common_cols].corr(), k=1)]
                synth_corr = synth_df[common_cols].corr().values[np.triu_indices_from(synth_df[common_cols].corr(), k=1)]

                axes[1, 1].scatter(real_corr, synth_corr, alpha=0.7)
                axes[1, 1].plot([-1, 1], [-1, 1], 'r--', alpha=0.8)
                axes[1, 1].set_xlabel('Real Data Correlations')
                axes[1, 1].set_ylabel('Synthetic Data Correlations')
                axes[1, 1].set_title('Correlation Comparison')
                axes[1, 1].grid(True, alpha=0.3)
            else:
                axes[1, 1].text(0.5, 0.5, 'Not enough columns\nfor correlation analysis', 
                               ha='center', va='center', transform=axes[1, 1].transAxes)

            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'{table_name}_statistics.png'), dpi=300, bbox_inches='tight')
            plt.close()

    def _generate_correlation_plots(self, output_dir: str):
        """Generate correlation heatmaps"""
        for table_name in ['main', 'performance', 'conversions']:
            if table_name not in self.synthetic_data or table_name not in self.real_data:
                continue

            real_df = self.real_data[table_name]
            synth_df = self.synthetic_data[table_name]

            # Get numerical columns
            num_cols = real_df.select_dtypes(include=np.number).columns
            common_cols = list(set(num_cols) & set(synth_df.columns))

            if len(common_cols) < 3:
                continue

            # Create correlation heatmaps
            fig, axes = plt.subplots(1, 2, figsize=(16, 6))

            # Real data correlation
            real_corr = real_df[common_cols].corr()
            sns.heatmap(real_corr, annot=True, cmap='coolwarm', center=0, 
                       square=True, ax=axes[0], cbar_kws={'shrink': 0.8})
            axes[0].set_title(f'Real Data Correlations - {table_name}')

            # Synthetic data correlation
            synth_corr = synth_df[common_cols].corr()
            sns.heatmap(synth_corr, annot=True, cmap='coolwarm', center=0, 
                       square=True, ax=axes[1], cbar_kws={'shrink': 0.8})
            axes[1].set_title(f'Synthetic Data Correlations - {table_name}')

            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'{table_name}_correlations.png'), dpi=300, bbox_inches='tight')
            plt.close()

    def _generate_summary_dashboard(self, output_dir: str):
        """Generate a summary dashboard with key metrics"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # Table shapes comparison
        table_names = ['main', 'performance', 'conversions']
        real_shapes = []
        synth_shapes = []

        for table in table_names:
            if table in self.real_data and table in self.synthetic_data:
                real_shapes.append(self.real_data[table].shape[0])
                synth_shapes.append(self.synthetic_data[table].shape[0])
            else:
                real_shapes.append(0)
                synth_shapes.append(0)

        x = np.arange(len(table_names))
        width = 0.35

        axes[0, 0].bar(x - width/2, real_shapes, width, label='Real', alpha=0.8)
        axes[0, 0].bar(x + width/2, synth_shapes, width, label='Synthetic', alpha=0.8)
        axes[0, 0].set_xlabel('Tables')
        axes[0, 0].set_ylabel('Number of Rows')
        axes[0, 0].set_title('Dataset Sizes Comparison')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(table_names)
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Add more summary plots as needed
        for i in range(1, 6):
            row = i // 3
            col = i % 3
            axes[row, col].text(0.5, 0.5, f'Summary Plot {i}\n(To be implemented)', 
                               ha='center', va='center', transform=axes[row, col].transAxes)
            axes[row, col].set_title(f'Summary Metric {i}')

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'evaluation_summary.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def generate_single_table_plots(self, table_name: str, output_dir: str):
        """Generate plots for a single table (synthetic only)"""
        if table_name not in self.synthetic_data:
            self.logger.warning(f"Table {table_name} not found in synthetic data")
            return

        df = self.synthetic_data[table_name]
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Generate basic distribution plots
        num_cols = df.select_dtypes(include=np.number).columns
        
        if len(num_cols) > 0:
            n_cols = min(4, len(num_cols))
            n_rows = (len(num_cols) + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(5*n_cols, 4*n_rows))
            if n_rows == 1 and n_cols == 1:
                axes = [axes]
            elif n_rows == 1:
                axes = axes
            else:
                axes = axes.flatten()

            for i, col in enumerate(num_cols):
                if i >= len(axes):
                    break

                ax = axes[i]
                try:
                    df[col].hist(bins=30, ax=ax, alpha=0.7)
                    ax.set_title(f'{col} Distribution')
                    ax.set_xlabel(col)
                    ax.set_ylabel('Frequency')
                    ax.grid(True, alpha=0.3)
                except Exception as e:
                    self.logger.warning(f"Could not plot {col}: {str(e)}")

            # Hide empty subplots
            for i in range(len(num_cols), len(axes)):
                axes[i].set_visible(False)

            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'{table_name}_synthetic_distributions.png'), 
                       dpi=300, bbox_inches='tight')
            plt.close()

        self.logger.info(f"Single table plots for {table_name} saved to {output_dir}")
