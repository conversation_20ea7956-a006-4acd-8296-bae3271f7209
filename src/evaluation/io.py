import json
import logging
import os
import pickle
from datetime import datetime
from typing import Any, Dict, Optional

import numpy as np
import pandas as pd


class ResultsManager:
    """
    Specialized manager for saving and loading evaluation results.
    """

    def __str__(self) -> str:
        return "RESULTS-MANAGER"

    def __init__(self):
        """Initialize results manager"""
        self.logger = logging.getLogger(str(self))

    def save_enhanced_results(self, results: Dict[str, Any], output_dir: str):
        """
        Save comprehensive evaluation results to multiple formats

        Args:
            results: Dictionary containing evaluation results
            output_dir: Directory to save results
        """
        try:
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)

            # Validate results format
            if not isinstance(results, dict):
                self.logger.warning(f"Results is not a dictionary, converting: {type(results)}")
                results = {
                    'evaluation_status': 'format_error',
                    'original_results': str(results),
                    'message': 'Results were not in expected dictionary format'
                }

            # Add timestamp and metadata
            enhanced_results = {
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'evaluation_type': 'enhanced_gan_evaluation',
                    'version': '2.0'
                },
                'results': results
            }

            # Save as JSON (human-readable)
            try:
                json_path = os.path.join(output_dir, 'evaluation_results.json')
                self._save_as_json(enhanced_results, json_path)
            except Exception as e:
                self.logger.warning(f"Failed to save JSON: {str(e)}")

            # Save as pickle (preserves data types)
            try:
                pickle_path = os.path.join(output_dir, 'evaluation_results.pkl')
                self._save_as_pickle(enhanced_results, pickle_path)
            except Exception as e:
                self.logger.warning(f"Failed to save pickle: {str(e)}")

            # Generate summary report
            try:
                summary_path = os.path.join(output_dir, 'evaluation_summary.txt')
                self._generate_summary_report(enhanced_results, summary_path)
            except Exception as e:
                self.logger.warning(f"Failed to generate summary: {str(e)}")

            # Generate CSV exports for key metrics
            try:
                self._export_key_metrics_csv(results, output_dir)
            except Exception as e:
                self.logger.warning(f"Failed to export CSV metrics: {str(e)}")

            self.logger.info(f"Enhanced evaluation results saved to {output_dir}")

        except Exception as e:
            self.logger.error(f"Error saving enhanced results: {str(e)}")
            # Don't raise - just log the error and continue

    def _save_as_json(self, results: Dict[str, Any], file_path: str):
        """Save results as JSON with custom serialization for special types"""
        def json_serializer(obj):
            """Custom JSON serializer for pandas/numpy objects"""
            if isinstance(obj, (pd.Timestamp, pd.DatetimeIndex)):
                return obj.isoformat()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif pd.isna(obj):
                return None
            return str(obj)

        with open(file_path, 'w') as f:
            json.dump(results, f, indent=2, default=json_serializer)

    def _save_as_pickle(self, results: Dict[str, Any], file_path: str):
        """Save results as pickle file"""
        with open(file_path, 'wb') as f:
            pickle.dump(results, f)

    def _generate_summary_report(self, results: Dict[str, Any], file_path: str):
        """Generate a human-readable summary report"""
        with open(file_path, 'w') as f:
            f.write("=" * 80 + "\n")
            f.write("ENHANCED GAN EVALUATION SUMMARY REPORT\n")
            f.write("=" * 80 + "\n\n")

            # Metadata
            metadata = results.get('metadata', {})
            f.write(f"Evaluation Date: {metadata.get('timestamp', 'Unknown')}\n")
            f.write(f"Evaluation Type: {metadata.get('evaluation_type', 'Unknown')}\n")
            f.write(f"Version: {metadata.get('version', 'Unknown')}\n\n")

            # Results summary
            eval_results = results.get('results', {})

            # Statistical comparison summary
            if 'statistical_comparison' in eval_results:
                f.write("STATISTICAL COMPARISON SUMMARY\n")
                f.write("-" * 40 + "\n")
                stats = eval_results['statistical_comparison']

                # Check if statistical comparison was skipped
                if isinstance(stats, dict) and 'status' in stats and stats['status'] == 'skipped':
                    f.write(f"  Status: {stats['message']}\n")
                elif isinstance(stats, dict) and 'status' not in stats:
                    # Normal statistical results
                    for table, table_data in stats.items():
                        if isinstance(table_data, dict):
                            f.write(f"\n{table.upper()} Table:\n")
                            f.write(f"  Columns analyzed: {len(table_data)}\n")

                    if table_data:
                        # Calculate average differences
                        mean_diffs = []
                        for col, col_data in table_data.items():
                            if 'differences' in col_data and 'mean_diff_pct' in col_data['differences']:
                                mean_diffs.append(col_data['differences']['mean_diff_pct'])

                        if mean_diffs:
                            avg_diff = np.mean(mean_diffs)
                            f.write(f"  Average mean difference: {avg_diff:.2f}%\n")

            # Distribution comparison summary
            if 'distribution_comparison' in eval_results:
                f.write("\n\nDISTRIBUTION COMPARISON SUMMARY\n")
                f.write("-" * 40 + "\n")
                dist = eval_results['distribution_comparison']

                # Check if distribution comparison was skipped
                if isinstance(dist, dict) and 'status' in dist and dist['status'] == 'skipped':
                    f.write(f"  Status: {dist['message']}\n")
                elif isinstance(dist, dict) and 'status' not in dist:
                    # Normal distribution results
                    for table, table_data in dist.items():
                        if isinstance(table_data, dict):
                            f.write(f"\n{table.upper()} Table:\n")

                    similar_count = 0
                    total_count = 0

                    for col, col_data in table_data.items():
                        if 'ks_similar' in col_data:
                            total_count += 1
                            if col_data['ks_similar']:
                                similar_count += 1

                    if total_count > 0:
                        similarity_pct = (similar_count / total_count) * 100
                        f.write(f"  Similar distributions: {similar_count}/{total_count} ({similarity_pct:.1f}%)\n")

            # Integrity comparison summary
            if 'integrity_comparison' in eval_results:
                f.write("\n\nINTEGRITY COMPARISON SUMMARY\n")
                f.write("-" * 40 + "\n")
                integrity = eval_results['integrity_comparison']

                for table, table_data in integrity.items():
                    if table == 'cross_table':
                        f.write(f"\nCROSS-TABLE Integrity:\n")
                        for check_name, check_data in table_data.items():
                            if 'integrity_score' in check_data:
                                score = check_data['integrity_score'] * 100
                                f.write(f"  {check_name}: {score:.1f}%\n")
                    else:
                        f.write(f"\n{table.upper()} Table Integrity:\n")

                        # Currency consistency
                        if 'currency_consistency' in table_data:
                            currency = table_data['currency_consistency']
                            if 'difference_percentage_points' in currency:
                                diff = currency['difference_percentage_points']
                                f.write(f"  Currency consistency difference: {diff:.2f} percentage points\n")

            f.write("\n" + "=" * 80 + "\n")

    def _export_key_metrics_csv(self, results: Dict[str, Any], output_dir: str):
        """Export key metrics to CSV files for easy analysis"""
        try:
            # Statistical metrics CSV
            if 'statistical_comparison' in results:
                stats_comparison = results['statistical_comparison']

                # Only process if it's not a skipped status
                if isinstance(stats_comparison, dict) and 'status' not in stats_comparison:
                    stats_data = []
                    for table, table_data in stats_comparison.items():
                        if isinstance(table_data, dict):
                            for col, col_data in table_data.items():
                                if isinstance(col_data, dict) and 'differences' in col_data:
                                    row = {
                                        'table': table,
                                        'column': col,
                                        'real_mean': col_data['real']['mean'],
                                        'synthetic_mean': col_data['synthetic']['mean'],
                                        'mean_diff_pct': col_data['differences'].get('mean_diff_pct', 0),
                                        'real_std': col_data['real']['std'],
                                        'synthetic_std': col_data['synthetic']['std'],
                                        'std_diff_pct': col_data['differences'].get('std_diff_pct', 0)
                                    }
                                    stats_data.append(row)

                    if stats_data:
                        stats_df = pd.DataFrame(stats_data)
                        stats_df.to_csv(os.path.join(output_dir, 'statistical_metrics.csv'), index=False)

            # Distribution metrics CSV
            if 'distribution_comparison' in results:
                dist_comparison = results['distribution_comparison']

                # Only process if it's not a skipped status
                if isinstance(dist_comparison, dict) and 'status' not in dist_comparison:
                    dist_data = []
                    for table, table_data in dist_comparison.items():
                        if isinstance(table_data, dict):
                            for col, col_data in table_data.items():
                                if isinstance(col_data, dict) and 'ks_statistic' in col_data:
                                    row = {
                                        'table': table,
                                        'column': col,
                                        'ks_statistic': col_data['ks_statistic'],
                                        'ks_p_value': col_data['ks_p_value'],
                                        'ks_similar': col_data['ks_similar'],
                                        'wasserstein_distance': col_data['wasserstein_distance']
                                    }
                                    dist_data.append(row)

                    if dist_data:
                        dist_df = pd.DataFrame(dist_data)
                        dist_df.to_csv(os.path.join(output_dir, 'distribution_metrics.csv'), index=False)

        except Exception as e:
            self.logger.warning(f"Could not export CSV metrics: {str(e)}")

    def load_results(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Load evaluation results from file

        Args:
            file_path: Path to results file (JSON or pickle)

        Returns:
            Dictionary containing evaluation results or None if failed
        """
        try:
            if file_path.endswith('.json'):
                with open(file_path, 'r') as f:
                    return json.load(f)
            elif file_path.endswith('.pkl'):
                with open(file_path, 'rb') as f:
                    return pickle.load(f)
            else:
                self.logger.error(f"Unsupported file format: {file_path}")
                return None

        except Exception as e:
            self.logger.error(f"Error loading results from {file_path}: {str(e)}")
            return None

    def compare_evaluation_runs(self, results1: Dict[str, Any], results2: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compare results from two different evaluation runs

        Args:
            results1: First evaluation results
            results2: Second evaluation results

        Returns:
            Dictionary containing comparison analysis
        """
        comparison = {
            'metadata': {
                'comparison_timestamp': datetime.now().isoformat(),
                'run1_timestamp': results1.get('metadata', {}).get('timestamp', 'Unknown'),
                'run2_timestamp': results2.get('metadata', {}).get('timestamp', 'Unknown')
            },
            'differences': {}
        }

        try:
            # Compare statistical metrics
            if ('statistical_comparison' in results1.get('results', {}) and
                'statistical_comparison' in results2.get('results', {})):

                stats_comparison = self._compare_statistical_metrics(
                    results1['results']['statistical_comparison'],
                    results2['results']['statistical_comparison']
                )
                comparison['differences']['statistical_metrics'] = stats_comparison

            # Add more comparison logic as needed

        except Exception as e:
            self.logger.error(f"Error comparing evaluation runs: {str(e)}")

        return comparison

    def _compare_statistical_metrics(self, stats1: Dict[str, Any], stats2: Dict[str, Any]) -> Dict[str, Any]:
        """Compare statistical metrics between two runs"""
        comparison = {}

        for table in stats1.keys():
            if table in stats2:
                table_comparison = {}

                for col in stats1[table].keys():
                    if col in stats2[table]:
                        col1 = stats1[table][col]
                        col2 = stats2[table][col]

                        if 'differences' in col1 and 'differences' in col2:
                            mean_diff_change = (col2['differences'].get('mean_diff_pct', 0) -
                                              col1['differences'].get('mean_diff_pct', 0))

                            table_comparison[col] = {
                                'mean_diff_change': mean_diff_change,
                                'improvement': mean_diff_change < 0  # Lower difference is better
                            }

                comparison[table] = table_comparison

        return comparison
