import concurrent.futures
import logging
from typing import Any, Dict, Optional

import numpy as np
import pandas as pd

try:
    from src.config import settings
    SETTINGS_AVAILABLE = True
except (ImportError, KeyError):
    SETTINGS_AVAILABLE = False


class DateEvaluator:
    """
    Specialized evaluator for date and temporal analysis.
    """

    def __str__(self) -> str:
        return "DATE-EVALUATOR"

    def __init__(self, synthetic_data: Dict[str, Any], real_data: Optional[Dict[str, Any]]):
        """
        Initialize date evaluator

        Args:
            synthetic_data: Dictionary containing synthetic data tables
            real_data: Dictionary containing real data tables for comparison
        """
        self.logger = logging.getLogger(str(self))
        self.synthetic_data = synthetic_data
        self.real_data = real_data

    def compare_dates(self):
        """Compare date characteristics between real and synthetic data with parallel processing"""
        comparison = {}

        # Default date columns - can be overridden by settings if available
        date_cols = {
            'main': ['mediarow_start_date', 'mediarow_end_date', 'mediaplan_start_date', 'mediaplan_end_date'],
            'performance': [],
            'conversions': []
        }

        # Try to get from settings if available
        if SETTINGS_AVAILABLE:
            try:
                date_cols = settings.PREPROCESSING_PARAMS["date_columns"]
            except (KeyError, AttributeError):
                pass  # Use defaults

        def process_table_dates(table):
            """Process date comparisons for a single table"""
            if not self.real_data or table not in self.real_data or table not in self.synthetic_data:
                return table, {}

            real_df = self.real_data[table]
            synth_df = self.synthetic_data[table]

            table_comparison = {}

            for col in date_cols.get(table, []):
                if col in real_df.columns and col in synth_df.columns:
                    try:
                        real_dates = pd.to_datetime(real_df[col], errors='coerce').dropna()
                        synth_dates = pd.to_datetime(synth_df[col], errors='coerce').dropna()

                        if len(real_dates) > 0 and len(synth_dates) > 0:
                            table_comparison[col] = {
                                'real_date_range': {
                                    'min': real_dates.min(),
                                    'max': real_dates.max(),
                                    'range_days': (real_dates.max() - real_dates.min()).days
                                },
                                'synthetic_date_range': {
                                    'min': synth_dates.min(),
                                    'max': synth_dates.max(),
                                    'range_days': (synth_dates.max() - synth_dates.min()).days
                                },
                                'overlap_analysis': {
                                    'real_within_synth_range': (
                                        (real_dates >= synth_dates.min()) &
                                        (real_dates <= synth_dates.max())
                                    ).mean(),
                                    'synth_within_real_range': (
                                        (synth_dates >= real_dates.min()) &
                                        (synth_dates <= real_dates.max())
                                    ).mean()
                                }
                            }
                    except Exception as e:
                        self.logger.warning(f"Could not compare dates for {table}.{col}: {str(e)}")
                        continue

            return table, table_comparison

        # Process tables in parallel
        self.logger.info("Running date comparison in parallel...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            future_to_table = {
                executor.submit(process_table_dates, table): table
                for table in ['main', 'performance', 'conversions']
            }

            for future in concurrent.futures.as_completed(future_to_table):
                table, table_comparison = future.result()
                comparison[table] = table_comparison

        return comparison

    def analyze_temporal_patterns(self) -> Dict[str, Any]:
        """
        Analyze temporal patterns in synthetic data

        Returns:
            Dict containing temporal pattern analysis
        """
        patterns = {}

        # Default date columns - can be overridden by settings if available
        date_cols = {
            'main': ['mediarow_start_date', 'mediarow_end_date', 'mediaplan_start_date', 'mediaplan_end_date'],
            'performance': [],
            'conversions': []
        }

        # Try to get from settings if available
        if SETTINGS_AVAILABLE:
            try:
                date_cols = settings.PREPROCESSING_PARAMS["date_columns"]
            except (KeyError, AttributeError):
                pass  # Use defaults

        for table_name, columns in date_cols.items():
            if table_name not in self.synthetic_data:
                continue

            df = self.synthetic_data[table_name]
            table_patterns = {}

            for col in columns:
                if col in df.columns:
                    try:
                        dates = pd.to_datetime(df[col], errors='coerce').dropna()

                        if len(dates) > 0:
                            # Basic temporal statistics
                            table_patterns[col] = {
                                'date_range': {
                                    'min_date': dates.min(),
                                    'max_date': dates.max(),
                                    'span_days': (dates.max() - dates.min()).days
                                },
                                'temporal_distribution': {
                                    'year_range': dates.dt.year.max() - dates.dt.year.min() + 1,
                                    'month_distribution': dates.dt.month.value_counts().to_dict(),
                                    'day_of_week_distribution': dates.dt.dayofweek.value_counts().to_dict(),
                                    'quarter_distribution': dates.dt.quarter.value_counts().to_dict()
                                },
                                'data_quality': {
                                    'null_count': df[col].isnull().sum(),
                                    'valid_dates': len(dates),
                                    'invalid_dates': len(df) - len(dates),
                                    'completeness_rate': len(dates) / len(df) if len(df) > 0 else 0
                                }
                            }

                            # Seasonal patterns
                            if len(dates) > 30:  # Only if we have enough data
                                table_patterns[col]['seasonal_patterns'] = self._analyze_seasonal_patterns(dates)

                    except Exception as e:
                        self.logger.warning(f"Could not analyze temporal patterns for {table_name}.{col}: {str(e)}")
                        continue

            patterns[table_name] = table_patterns

        return patterns

    def _analyze_seasonal_patterns(self, dates: pd.Series) -> Dict[str, Any]:
        """
        Analyze seasonal patterns in date series

        Args:
            dates: Series of datetime values

        Returns:
            Dict containing seasonal pattern analysis
        """
        patterns = {}

        try:
            # Monthly patterns
            monthly_counts = dates.dt.month.value_counts().sort_index()
            patterns['monthly_variation'] = {
                'coefficient_of_variation': monthly_counts.std() / monthly_counts.mean() if monthly_counts.mean() > 0 else 0,
                'peak_month': monthly_counts.idxmax(),
                'low_month': monthly_counts.idxmin(),
                'seasonal_strength': (monthly_counts.max() - monthly_counts.min()) / monthly_counts.mean() if monthly_counts.mean() > 0 else 0
            }

            # Quarterly patterns
            quarterly_counts = dates.dt.quarter.value_counts().sort_index()
            patterns['quarterly_variation'] = {
                'coefficient_of_variation': quarterly_counts.std() / quarterly_counts.mean() if quarterly_counts.mean() > 0 else 0,
                'peak_quarter': quarterly_counts.idxmax(),
                'low_quarter': quarterly_counts.idxmin()
            }

            # Day of week patterns
            dow_counts = dates.dt.dayofweek.value_counts().sort_index()
            patterns['weekly_variation'] = {
                'coefficient_of_variation': dow_counts.std() / dow_counts.mean() if dow_counts.mean() > 0 else 0,
                'peak_day': dow_counts.idxmax(),  # 0=Monday, 6=Sunday
                'low_day': dow_counts.idxmin(),
                'weekday_vs_weekend_ratio': dow_counts[:5].sum() / dow_counts[5:].sum() if dow_counts[5:].sum() > 0 else float('inf')
            }

        except Exception as e:
            self.logger.warning(f"Error in seasonal pattern analysis: {str(e)}")

        return patterns

    def validate_date_consistency(self) -> Dict[str, Any]:
        """
        Validate date consistency within and across tables

        Returns:
            Dict containing date consistency validation results
        """
        validation_results = {}

        # Check date pairs within tables (start/end dates)
        date_pairs = [
            ('main', 'mediarow_start_date', 'mediarow_end_date'),
            ('main', 'mediaplan_start_date', 'mediaplan_end_date')
        ]

        for table_name, start_col, end_col in date_pairs:
            if table_name in self.synthetic_data:
                df = self.synthetic_data[table_name]

                if start_col in df.columns and end_col in df.columns:
                    try:
                        start_dates = pd.to_datetime(df[start_col], errors='coerce')
                        end_dates = pd.to_datetime(df[end_col], errors='coerce')

                        valid_pairs = ~(start_dates.isna() | end_dates.isna())

                        if valid_pairs.sum() > 0:
                            valid_start = start_dates[valid_pairs]
                            valid_end = end_dates[valid_pairs]

                            # Check temporal order
                            correct_order = (valid_start <= valid_end).sum()
                            total_valid = len(valid_start)

                            validation_results[f'{table_name}_{start_col}_to_{end_col}'] = {
                                'total_pairs': len(df),
                                'valid_pairs': total_valid,
                                'correct_temporal_order': correct_order,
                                'temporal_consistency_rate': correct_order / total_valid if total_valid > 0 else 0,
                                'average_duration_days': (valid_end - valid_start).dt.days.mean(),
                                'max_duration_days': (valid_end - valid_start).dt.days.max(),
                                'min_duration_days': (valid_end - valid_start).dt.days.min()
                            }

                    except Exception as e:
                        self.logger.warning(f"Could not validate date consistency for {table_name} {start_col}-{end_col}: {str(e)}")

        return validation_results

    def calculate_temporal_summary(self, date_comparison: Dict[str, Any],
                                 consistency_validation: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate overall temporal quality summary

        Args:
            date_comparison: Results from compare_dates()
            consistency_validation: Results from validate_date_consistency()

        Returns:
            Dict containing temporal summary
        """
        summary = {
            'total_date_columns': 0,
            'valid_date_columns': 0,
            'temporal_consistency_score': 0,
            'date_range_coverage_score': 0,
            'overall_temporal_score': 0
        }

        # Count date columns and calculate coverage
        coverage_scores = []
        consistency_scores = []

        for table, table_data in date_comparison.items():
            for col, col_data in table_data.items():
                summary['total_date_columns'] += 1
                summary['valid_date_columns'] += 1

                # Calculate range coverage score
                if 'overlap_analysis' in col_data:
                    overlap = col_data['overlap_analysis']
                    coverage_score = (overlap['real_within_synth_range'] + overlap['synth_within_real_range']) / 2
                    coverage_scores.append(coverage_score)

        # Calculate consistency scores
        for validation_key, validation_data in consistency_validation.items():
            if 'temporal_consistency_rate' in validation_data:
                consistency_scores.append(validation_data['temporal_consistency_rate'])

        # Calculate overall scores
        if coverage_scores:
            summary['date_range_coverage_score'] = np.mean(coverage_scores) * 100

        if consistency_scores:
            summary['temporal_consistency_score'] = np.mean(consistency_scores) * 100

        # Overall temporal score (weighted average)
        if coverage_scores or consistency_scores:
            scores = []
            if coverage_scores:
                scores.append(summary['date_range_coverage_score'])
            if consistency_scores:
                scores.append(summary['temporal_consistency_score'])

            summary['overall_temporal_score'] = np.mean(scores)

        return summary
