import logging
import os
from typing import Any, Dict, Optional

import numpy as np
import pandas as pd

from .dates import DateEvaluator
from .distributions import DistributionEvaluator
from .integrity import IntegrityEvaluator
from .io import ResultsManager
from .statistics import StatisticsEvaluator
from .visualizations import VisualizationGenerator

try:
    from src.config import settings
    SETTINGS_AVAILABLE = True
except (ImportError, KeyError):
    SETTINGS_AVAILABLE = False


class EnhancedGANEvaluator:
    """
    Main orchestrator for comprehensive GAN evaluation.
    Coordinates specialized evaluators for different aspects of data quality.
    """

    def __str__(self) -> str:
        return "ENHANCED-GAN-EVALUATOR"

    def __init__(self, synthetic_data: Dict[str, Any], real_data: Optional[Dict[str, Any]],
                 use_real_data=True, fast_mode=False):
        """
        Initialize enhanced evaluator with real and synthetic data comparison

        Args:
            synthetic_data: Dictionary containing synthetic data tables
            real_data: Dictionary containing real data tables for comparison
            use_real_data: Whether to use real data for comparison
            fast_mode: Whether to run in fast mode (no plots, sample real data)
        """
        self.logger = logging.getLogger(str(self))
        self.fast_mode = fast_mode

        # Load preprocessor (optional) - defer loading to avoid import issues
        self.preprocessor = None

        self.synthetic_data = synthetic_data
        self.real_data = real_data

        # Sample real data in fast mode to speed up processing
        if use_real_data and real_data is not None:
            self.logger.info("Loading real data for comparison...")

            if self.fast_mode:
                sample_size = 10000  # Default sample size
                random_state = 42   # Default random state
                self.real_data = {
                    'main': real_data['main'].sample(n=min(sample_size, len(real_data['main'])),
                                                   random_state=random_state),
                    'performance': real_data['performance'].sample(n=min(sample_size, len(real_data['performance'])),
                                                                 random_state=random_state),
                    'conversions': real_data['conversions'].sample(n=min(sample_size, len(real_data['conversions'])),
                                                                 random_state=random_state)
                }
                self.logger.info(f"Fast mode: Sampled real data to {sample_size} rows per table")

            self.logger.info("Real data loaded successfully")
            self.logger.info(f"Real data shapes - Main: {self.real_data['main'].shape}, "
                           f"Performance: {self.real_data['performance'].shape}, "
                           f"Conversions: {self.real_data['conversions'].shape}")

        # Clean data before evaluation to prevent concatenated string issues
        self.synthetic_data = self._clean_data_for_evaluation(self.synthetic_data)
        if self.real_data:
            self.real_data = self._clean_data_for_evaluation(self.real_data)

        # Initialize specialized evaluators
        self.statistics_evaluator = StatisticsEvaluator(self.synthetic_data, self.real_data)
        self.distribution_evaluator = DistributionEvaluator(self.synthetic_data, self.real_data)
        self.date_evaluator = DateEvaluator(self.synthetic_data, self.real_data)
        self.integrity_evaluator = IntegrityEvaluator(self.synthetic_data, self.real_data)
        self.visualization_generator = VisualizationGenerator(self.synthetic_data, self.real_data, self.fast_mode)
        self.results_manager = ResultsManager()

        # Convert date columns
        self._convert_date_columns()

    def _convert_date_columns(self):
        """Convert date columns to datetime format for both real and synthetic data"""
        # Default date columns - can be overridden by settings if available
        date_cols = {
            'main': ['mediarow_start_date', 'mediarow_end_date', 'mediaplan_start_date', 'mediaplan_end_date'],
            'performance': [],
            'conversions': []
        }

        # Try to get from settings if available
        if SETTINGS_AVAILABLE:
            try:
                date_cols = settings.PREPROCESSING_PARAMS["date_columns"]
            except (KeyError, AttributeError):
                pass  # Use defaults

        # Convert synthetic data dates
        for table, cols in date_cols.items():
            if table in self.synthetic_data:
                for col in cols:
                    if col in self.synthetic_data[table].columns:
                        self.synthetic_data[table][col] = pd.to_datetime(
                            self.synthetic_data[table][col], errors='coerce'
                        )

        # Convert real data dates if available
        if self.real_data:
            for table, cols in date_cols.items():
                if table in self.real_data:
                    for col in cols:
                        if col in self.real_data[table].columns:
                            self.real_data[table][col] = pd.to_datetime(
                                self.real_data[table][col], errors='coerce'
                            )

    def evaluate_all(self):
        """
        Comprehensive evaluation comparing synthetic vs real data
        """
        try:
            # Create evaluation directory structure with default paths
            eval_dir = 'data/evaluation'
            plots_dir = 'data/plots'

            # Try to get from settings if available
            if SETTINGS_AVAILABLE:
                try:
                    eval_dir = settings.PATHS['EVAL_DIR']
                    plots_dir = settings.PATHS['PLOTS_DIR']
                except (KeyError, AttributeError):
                    pass  # Use defaults

            os.makedirs(eval_dir, exist_ok=True)
            os.makedirs(plots_dir, exist_ok=True)

            results = {}

            # Run evaluations if real data is available
            if self.real_data:
                self.logger.info("Running comprehensive evaluation with real data comparison...")

                # 1. Statistical comparison - Re-enabled with proper error handling
                try:
                    self.logger.info("Running statistical comparison...")
                    results['statistical_comparison'] = self.statistics_evaluator.compare_statistics()
                    self.logger.info("Statistical comparison completed")
                except Exception as e:
                    self.logger.warning(f"Statistical comparison failed: {str(e)}")
                    results['statistical_comparison'] = {
                        'status': 'failed',
                        'error': str(e),
                        'message': 'Statistical comparison failed but pipeline continued'
                    }

                # 2. Distribution comparison - Re-enabled with proper error handling
                try:
                    self.logger.info("Running distribution comparison...")
                    results['distribution_comparison'] = self.distribution_evaluator.compare_distributions()
                    self.logger.info("Distribution comparison completed")
                except Exception as e:
                    self.logger.warning(f"Distribution comparison failed: {str(e)}")
                    results['distribution_comparison'] = {
                        'status': 'failed',
                        'error': str(e),
                        'message': 'Distribution comparison failed but pipeline continued'
                    }

                # 3. Date analysis comparison
                try:
                    results['date_comparison'] = self.date_evaluator.compare_dates()
                    self.logger.info("Date comparison completed")
                except Exception as e:
                    self.logger.warning(f"Date comparison failed: {str(e)}")
                    results['date_comparison'] = {'status': 'failed', 'error': str(e)}

                # 4. Integrity checks comparison
                try:
                    results['integrity_comparison'] = self.integrity_evaluator.compare_integrity()
                    self.logger.info("Integrity comparison completed")
                except Exception as e:
                    self.logger.warning(f"Integrity comparison failed: {str(e)}")
                    results['integrity_comparison'] = {'status': 'failed', 'error': str(e)}

                # 5. Generate comparison visualizations (skip in fast mode)
                if not self.fast_mode:
                    try:
                        self.visualization_generator.generate_comparison_visuals(plots_dir)
                        self.logger.info("Visualization generation completed")
                    except Exception as e:
                        self.logger.warning(f"Visualization generation failed: {str(e)}")
                        results['visualization_status'] = {'status': 'failed', 'error': str(e)}
                else:
                    self.logger.info("Fast mode: Skipping visualization generation")
            else:
                # Fallback to synthetic-only analysis
                self.logger.warning("No real data available. Performing synthetic-only evaluation.")
                try:
                    results['synthetic_only'] = self._analyze_synthetic_only()
                    self.logger.info("Synthetic-only analysis completed")
                except Exception as e:
                    self.logger.warning(f"Synthetic-only analysis failed: {str(e)}")
                    results['synthetic_only'] = {'status': 'failed', 'error': str(e)}

            # Save results
            try:
                self.results_manager.save_enhanced_results(results, eval_dir)
                self.logger.info("Evaluation results saved successfully")
            except Exception as e:
                self.logger.warning(f"Failed to save evaluation results: {str(e)}")
                results['save_status'] = {'status': 'failed', 'error': str(e)}

            # Check if any evaluations succeeded
            successful_evaluations = [k for k, v in results.items()
                                    if isinstance(v, dict) and v.get('status') != 'failed']

            if successful_evaluations:
                self.logger.info(f"Evaluation completed with {len(successful_evaluations)} successful components")
                return results
            else:
                self.logger.warning("All evaluation components failed, but returning partial results")
                return results

        except Exception as e:
            self.logger.error(f"Critical error during enhanced evaluation: {str(e)}")
            # Return a minimal result instead of raising
            return {
                'evaluation_status': 'critical_failure',
                'error': str(e),
                'message': 'Evaluation encountered critical errors but pipeline continued'
            }

    def _clean_data_for_evaluation(self, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Clean data to prevent concatenated string issues in evaluation.

        Args:
            data: Dictionary of DataFrames to clean

        Returns:
            Cleaned data dictionary
        """
        cleaned_data = {}

        for table_name, df in data.items():
            cleaned_df = df.copy()

            # Clean string columns to prevent concatenation issues
            for col in cleaned_df.columns:
                if cleaned_df[col].dtype == 'object':
                    # Convert to string and handle NaN values
                    cleaned_df[col] = cleaned_df[col].astype(str)

                    # Replace 'nan' strings with actual NaN
                    cleaned_df[col] = cleaned_df[col].replace('nan', pd.NA)

                    # Replace any string longer than 100 characters with safe defaults
                    mask = cleaned_df[col].str.len() > 100
                    if mask.any():
                        self.logger.info(f"Cleaning {mask.sum()} long strings in {table_name}.{col}")
                        if 'currency' in col.lower():
                            cleaned_df.loc[mask, col] = 'EUR'
                        elif 'name' in col.lower():
                            cleaned_df.loc[mask, col] = 'Unknown'
                        else:
                            cleaned_df.loc[mask, col] = 'N/A'

                    # Ensure no empty strings that could cause issues
                    cleaned_df[col] = cleaned_df[col].replace('', 'N/A')

            # Ensure numeric columns are properly typed
            for col in cleaned_df.columns:
                if col not in ['date', 'timestamp'] and 'date' not in col.lower():
                    # Try to convert to numeric if it looks like it should be numeric
                    if cleaned_df[col].dtype == 'object':
                        try:
                            # Check if it's actually numeric data stored as strings
                            numeric_test = pd.to_numeric(cleaned_df[col], errors='coerce')
                            if not numeric_test.isna().all():  # If some values are numeric
                                cleaned_df[col] = numeric_test
                        except:
                            pass  # Keep as string if conversion fails

            cleaned_data[table_name] = cleaned_df

        return cleaned_data

    def _analyze_synthetic_only(self):
        """Fallback method for synthetic-only analysis when real data is unavailable"""
        stats = {}

        for table in ['main', 'performance', 'conversions']:
            if table in self.synthetic_data:
                df = self.synthetic_data[table]
                table_stats = {}

                # Basic statistics
                num_cols = df.select_dtypes(include=np.number).columns
                for col in num_cols:
                    if 'date' not in col.lower():
                        table_stats[f'{col}_mean'] = df[col].mean()
                        table_stats[f'{col}_std'] = df[col].std()
                        table_stats[f'{col}_min'] = df[col].min()
                        table_stats[f'{col}_max'] = df[col].max()

                stats[table] = table_stats

        return stats
