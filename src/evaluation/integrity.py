import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional


class IntegrityEvaluator:
    """
    Specialized evaluator for data integrity and cross-table validation.
    """

    def __str__(self) -> str:
        return "INTEGRITY-EVALUATOR"

    def __init__(self, synthetic_data: Dict[str, Any], real_data: Optional[Dict[str, Any]]):
        """
        Initialize integrity evaluator

        Args:
            synthetic_data: Dictionary containing synthetic data tables
            real_data: Dictionary containing real data tables for comparison
        """
        self.logger = logging.getLogger(str(self))
        self.synthetic_data = synthetic_data
        self.real_data = real_data

    def compare_integrity(self):
        """Compare data integrity patterns between real and synthetic data with enhanced cross-table validation"""
        comparison = {}

        self.logger.info("Running enhanced integrity comparison...")

        # Check if all required tables are available
        required_tables = ['main', 'performance', 'conversions']
        available_tables = [table for table in required_tables if table in self.synthetic_data]

        if not available_tables:
            self.logger.warning("No tables available for integrity comparison")
            return comparison

        # Individual table integrity checks
        for table in available_tables:
            if table == 'main':
                table_integrity = self._check_main_table_integrity()
            else:
                table_integrity = self._check_child_table_integrity(table)
            comparison[table] = table_integrity

        # Cross-table integrity checks
        if len(available_tables) > 1:
            cross_table_integrity = self._check_cross_table_integrity(available_tables)
            comparison['cross_table'] = cross_table_integrity

        self.logger.info("Enhanced integrity comparison completed")
        return comparison

    def _check_main_table_integrity(self):
        """Check integrity within the main table"""
        integrity_results = {}

        real_df = self.real_data.get('main') if self.real_data else None
        synth_df = self.synthetic_data['main']

        # 1. Currency Consistency Check
        if ('campaign_currency' in synth_df.columns and 'mediarow_currency' in synth_df.columns):
            try:
                # Synthetic data currency consistency
                synth_valid = synth_df.dropna(subset=['campaign_currency', 'mediarow_currency'])
                synth_total = len(synth_valid)
                synth_matching = (synth_valid['campaign_currency'] == synth_valid['mediarow_currency']).sum()
                synth_match_pct = (synth_matching / synth_total * 100) if synth_total > 0 else 0

                currency_result = {
                    'synthetic_matching_percentage': synth_match_pct,
                    'synthetic_total_rows': synth_total,
                }

                # Add real data comparison if available
                if (real_df is not None and
                    'campaign_currency' in real_df.columns and
                    'mediarow_currency' in real_df.columns):

                    real_valid = real_df.dropna(subset=['campaign_currency', 'mediarow_currency'])
                    real_total = len(real_valid)
                    real_matching = (real_valid['campaign_currency'] == real_valid['mediarow_currency']).sum()
                    real_match_pct = (real_matching / real_total * 100) if real_total > 0 else 0

                    currency_result.update({
                        'real_matching_percentage': real_match_pct,
                        'difference_percentage_points': abs(real_match_pct - synth_match_pct),
                        'real_total_rows': real_total,
                        'acceptable_difference': abs(real_match_pct - synth_match_pct) <= 10.0  # Default threshold
                    })

                integrity_results['currency_consistency'] = currency_result
            except Exception as e:
                self.logger.warning(f"Could not check currency consistency: {str(e)}")

        # 2. Date Consistency Checks
        date_pairs = [
            ('mediarow_start_date', 'mediarow_end_date'),
            ('mediaplan_start_date', 'mediaplan_end_date')
        ]

        date_integrity = {}
        for start_col, end_col in date_pairs:
            if start_col in synth_df.columns and end_col in synth_df.columns:
                try:
                    start_dates = pd.to_datetime(synth_df[start_col], errors='coerce')
                    end_dates = pd.to_datetime(synth_df[end_col], errors='coerce')

                    valid_pairs = ~(start_dates.isna() | end_dates.isna())
                    if valid_pairs.sum() > 0:
                        valid_start = start_dates[valid_pairs]
                        valid_end = end_dates[valid_pairs]

                        # Check if start dates are before end dates
                        correct_order = (valid_start <= valid_end).sum()
                        total_valid = len(valid_start)

                        date_integrity[f'{start_col}_to_{end_col}'] = {
                            'correct_temporal_order': correct_order,
                            'total_valid_pairs': total_valid,
                            'temporal_consistency_score': correct_order / total_valid if total_valid > 0 else 1.0
                        }
                except Exception as e:
                    self.logger.warning(f"Could not check date consistency for {start_col}-{end_col}: {str(e)}")

        if date_integrity:
            integrity_results['date_consistency'] = date_integrity

        # 3. ID Column Validity
        id_columns = [col for col in synth_df.columns if col.endswith('_id')]
        id_validity = {}

        for col in id_columns:
            try:
                # Check for non-null, positive integer values
                non_null = synth_df[col].notna().sum()
                total = len(synth_df)

                # Convert to numeric and check for positive values
                numeric_col = pd.to_numeric(synth_df[col], errors='coerce')
                positive_values = (numeric_col > 0).sum()

                id_validity[col] = {
                    'non_null_count': int(non_null),  # Convert numpy int to Python int
                    'total_count': int(total),
                    'non_null_percentage': float((non_null / total * 100) if total > 0 else 0),
                    'positive_values': int(positive_values),  # Convert numpy int to Python int
                    'validity_score': float((positive_values / total) if total > 0 else 0)
                }
            except Exception as e:
                self.logger.warning(f"Could not check ID validity for {col}: {str(e)}")

        if id_validity:
            integrity_results['id_column_validity'] = id_validity

        return integrity_results

    def _check_child_table_integrity(self, table_name: str):
        """Check integrity within child tables (performance, conversions)"""
        integrity_results = {}

        synth_df = self.synthetic_data[table_name]
        real_df = self.real_data.get(table_name) if self.real_data else None

        # 1. ID Column Validity
        id_columns = [col for col in synth_df.columns if col.endswith('_id')]
        id_validity = {}

        for col in id_columns:
            try:
                # Check for non-null, positive integer values
                non_null = synth_df[col].notna().sum()
                total = len(synth_df)

                # Convert to numeric and check for positive values
                numeric_col = pd.to_numeric(synth_df[col], errors='coerce')
                positive_values = (numeric_col > 0).sum()

                id_validity[col] = {
                    'non_null_count': int(non_null),  # Convert numpy int to Python int
                    'total_count': int(total),
                    'non_null_percentage': float((non_null / total * 100) if total > 0 else 0),
                    'positive_values': int(positive_values),  # Convert numpy int to Python int
                    'validity_score': float((positive_values / total) if total > 0 else 0)
                }
            except Exception as e:
                self.logger.warning(f"Could not check ID validity for {col}: {str(e)}")

        if id_validity:
            integrity_results['id_column_validity'] = id_validity

        # 2. Numerical Value Consistency
        numeric_cols = synth_df.select_dtypes(include=[np.number]).columns
        numeric_integrity = {}

        for col in numeric_cols:
            if 'id' not in col.lower() and 'date' not in col.lower():
                try:
                    # Check for reasonable value ranges
                    non_null_values = synth_df[col].dropna()

                    if len(non_null_values) > 0:
                        numeric_integrity[col] = {
                            'negative_values_count': int((non_null_values < 0).sum()),  # Convert to Python int
                            'zero_values_count': int((non_null_values == 0).sum()),
                            'positive_values_count': int((non_null_values > 0).sum()),
                            'extreme_values_count': int((np.abs(non_null_values) > non_null_values.quantile(0.99) * 10).sum()),
                            'value_range': float(non_null_values.max() - non_null_values.min()),  # Convert to Python float
                            'reasonableness_score': float(1.0 - ((non_null_values < 0).sum() / len(non_null_values)))
                        }
                except Exception as e:
                    self.logger.warning(f"Could not check numeric integrity for {col}: {str(e)}")

        if numeric_integrity:
            integrity_results['numeric_integrity'] = numeric_integrity

        return integrity_results

    def _check_cross_table_integrity(self, available_tables):
        """Check integrity across multiple tables"""
        cross_table_results = {}

        # Check client_id consistency across all tables
        client_id_integrity = self._check_id_consistency('client_id', available_tables)
        if client_id_integrity:
            cross_table_results['client_id_consistency'] = client_id_integrity

        # Check campaign_id consistency
        campaign_id_integrity = self._check_id_consistency('campaign_id', available_tables)
        if campaign_id_integrity:
            cross_table_results['campaign_id_consistency'] = campaign_id_integrity

        # Check mediarow_id consistency (if available)
        mediarow_id_integrity = self._check_id_consistency('mediarow_id', available_tables)
        if mediarow_id_integrity:
            cross_table_results['mediarow_id_consistency'] = mediarow_id_integrity

        return cross_table_results

    def _check_id_consistency(self, id_column: str, available_tables) -> Dict[str, Any]:
        """Check consistency of a specific ID column across tables"""
        integrity_info = {
            'column_name': id_column,
            'tables_with_column': [],
            'unique_values_per_table': {},
            'common_values': set(),
            'integrity_score': 0
        }

        tables_with_id = {}

        # Collect unique values from each table
        for table in available_tables:
            df = self.synthetic_data[table]
            if id_column in df.columns:
                integrity_info['tables_with_column'].append(table)

                # Get unique non-null values
                unique_values = set(df[id_column].dropna().astype(str))
                tables_with_id[table] = unique_values
                integrity_info['unique_values_per_table'][table] = len(unique_values)

        # Calculate common values and integrity score
        if len(tables_with_id) >= 2:
            table_names = list(tables_with_id.keys())

            # Find intersection of all tables
            common_values = tables_with_id[table_names[0]]
            for table in table_names[1:]:
                common_values = common_values & tables_with_id[table]

            integrity_info['common_values'] = len(common_values)

            # Calculate integrity score based on overlap
            total_overlaps = 0
            total_comparisons = 0

            for i in range(len(table_names)):
                for j in range(i + 1, len(table_names)):
                    table1, table2 = table_names[i], table_names[j]
                    values1, values2 = tables_with_id[table1], tables_with_id[table2]

                    if values1 and values2:
                        overlap = len(values1 & values2)
                        union = len(values1 | values2)
                        if union > 0:
                            total_overlaps += overlap / union
                        total_comparisons += 1

            if total_comparisons > 0:
                integrity_info['integrity_score'] = total_overlaps / total_comparisons

        return integrity_info

    def calculate_integrity_summary(self, integrity_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate overall integrity quality summary

        Args:
            integrity_results: Results from compare_integrity()

        Returns:
            Dict containing integrity summary
        """
        summary = {
            'total_integrity_checks': 0,
            'passed_checks': 0,
            'currency_consistency_score': 0,
            'temporal_consistency_score': 0,
            'id_validity_score': 0,
            'cross_table_integrity_score': 0,
            'overall_integrity_score': 0
        }

        scores = []

        # Process individual table results
        for table, table_data in integrity_results.items():
            if table == 'cross_table':
                continue

            # Currency consistency
            if 'currency_consistency' in table_data:
                currency_data = table_data['currency_consistency']
                if 'difference_percentage_points' in currency_data:
                    diff = currency_data['difference_percentage_points']
                    # Score: 100 if diff <= 5%, 50 if diff <= 15%, 0 if diff > 15%
                    if diff <= 5.0:
                        currency_score = 100
                    elif diff <= 15.0:
                        currency_score = 50
                    else:
                        currency_score = 0
                    summary['currency_consistency_score'] = currency_score
                    scores.append(currency_score)

            # Temporal consistency
            if 'date_consistency' in table_data:
                date_scores = [data['temporal_consistency_score'] * 100
                             for data in table_data['date_consistency'].values()]
                if date_scores:
                    temporal_score = np.mean(date_scores)
                    summary['temporal_consistency_score'] = temporal_score
                    scores.append(temporal_score)

            # ID validity
            if 'id_column_validity' in table_data:
                id_scores = [data['validity_score'] * 100
                           for data in table_data['id_column_validity'].values()]
                if id_scores:
                    id_score = np.mean(id_scores)
                    summary['id_validity_score'] = max(summary['id_validity_score'], id_score)

        # Add ID validity score to overall scores
        if summary['id_validity_score'] > 0:
            scores.append(summary['id_validity_score'])

        # Cross-table integrity
        if 'cross_table' in integrity_results:
            cross_table_scores = []
            for consistency_check in integrity_results['cross_table'].values():
                if 'integrity_score' in consistency_check:
                    cross_table_scores.append(consistency_check['integrity_score'] * 100)

            if cross_table_scores:
                cross_table_score = np.mean(cross_table_scores)
                summary['cross_table_integrity_score'] = cross_table_score
                scores.append(cross_table_score)

        # Overall integrity score
        summary['total_integrity_checks'] = len(scores)
        summary['passed_checks'] = len([s for s in scores if s >= 70])  # 70% threshold for "passing"

        if scores:
            summary['overall_integrity_score'] = np.mean(scores)

        return summary
