import logging
import os
from typing import Any, Dict, Optional

import pandas as pd

from src.models.models import OutputSchema, PayloadSchema
from .components.evaluation import EvaluationComponent
from .components.inference import InferenceComponent
from .components.input import InputComponent
from .components.logger import CustomFormatter
from .components.payload import Payload
from .components.training import TrainingComponent



class SyntheticData:
    """
    Main class for synthetic data generation pipeline.
    """

    def __str__(self) -> str:
        return "SYNTHETIC-DATA"

    def __init__(self) -> None:
        """Initialize the SyntheticData class

        Args:
            payload (PayloadSchema): validated payload
        """
        self.logger = logging.getLogger(str(self))
        self._initialize_logging()

    def _initialize_logging(self) -> None:
        """Initialize logging configuration"""
        # Configure logger
        handler = logging.StreamHandler()
        handler.setFormatter(CustomFormatter())
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def _train(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run the training process using database extraction

        Args:
            input_data (Dict[str, Any]): Input data for training

        Returns:
            Dict[str, Any]: Training results
        """


        # TODO: model needs to have only the media dim as input, like it will happen in production environment.abs
        # Adapt the training pipeline to do this.

        self.logger.info("Initializing TrainingComponent...")
        training_component = TrainingComponent(payload=self.payload, input_data=input_data)
        self.logger.info("Running training with joint_training=True...")
        result = training_component.run_training(joint_training=True)
        self.logger.info("Training component finished")
        return result

    def _generate(self, input_data: Dict[str, Any], num_samples: int = 1000) -> Dict[str, Any]:
        """Run the data generation process

        Args:
            input_data (Dict[str, Any]): Input data for generation
            num_samples (int, optional): Number of samples to generate

        Returns:
            Dict[str, Any]: Generation results
        """
        self.logger.info(f"Starting data generation for {num_samples} samples...")
        
        # Initialize inference component
        inference_component = InferenceComponent(payload=self.payload, input_data=input_data)
        

        # TODO: delete generation based on the 3 tables (we can't use it) and replace it with generation based on the media_dim only
        # Run generation
        generation_results = inference_component.run_generation(
            num_samples=num_samples,
            joint_training=True
        )
        
        # Store CSVs locally
        inference_component.save_synthetic_csvs(generation_results['generated_data'])

        return generation_results

    def _evaluate(self, synthetic_data: Dict[str, Any], real_data: Optional[Dict[str, Any]] = None, dataframes: Optional[list] = None) -> Dict[str, Any]:
        """Run the evaluation process

        Args:
            synthetic_data: Generated synthetic data
            real_data: Original real data for comparison
            dataframes: List of dataframes to evaluate (for local evaluation)

        Returns:
            Dict[str, Any]: Evaluation results
        """

        return EvaluationComponent(
            payload=self.payload,
            synthetic_data=synthetic_data,
            real_data=real_data,
            dataframes=dataframes
        ).run_evaluation()


    def main(self, payload: PayloadSchema) -> OutputSchema:
        """Main method of the SyntheticData class

        Returns:
            OutputSchema: output of the synthetic data generation
        """
        try:
            self.logger.info("Starting synthetic data pipeline")

            # Initialize and validate payload
            self.logger.info("Validating payload...")
            self.payload = Payload.validated_payload(payload)
            self.logger.info(f"Payload validated successfully: {self.payload.run_type}")

            # Initialize input component
            input_component = InputComponent(payload=self.payload)

            # Training Only Pipeline
            if self.payload.run_type == 'training':
                self.logger.info("Running TRAINING ONLY pipeline")

                # Extract training data
                self.logger.info("Loading training data...")
                input_data = input_component.load_training_data()
    
                # Run training pipeline
                self.logger.info("Starting training pipeline...")
                training_results = self._train(input_data=input_data)
                
                if training_results['status'] != 'success':
                    self.logger.error("Training failed")
                    return OutputSchema(status='failed', error=training_results.get('error'))


                # TODO: add validation after training


                self.logger.info("Training completed successfully")
                return OutputSchema(
                    status='success',
                    training_results=training_results
                )

            # Generation Only Pipeline
            elif self.payload.run_type == 'generation':
                self.logger.info("Running GENERATION ONLY pipeline")

                # Check if models exist
                if not (os.path.exists("models_local/multi_table_gan.pth") and
                       os.path.exists("models_local/preprocessor.pkl")):
                    return OutputSchema(status='failed', error="No trained models found. Run training first.")


                # TODO: Use real media_dim as input data for generation purpose instead of dummy data
                # Create minimal dummy input data for generation (no database needed)
                self.logger.info("Using existing models for generation (no database connection needed)")
                dummy_input = {
                    'data': {
                        'main': pd.DataFrame({'client_id': [1]}),
                        'performance': pd.DataFrame({'client_id': [1]}),
                        'conversions': pd.DataFrame({'client_id': [1]})
                    }
                }


                # TODO: Number of samples should be inferred from the media dim based on the amount of mediarows and the length (in days, end-start date) of each mediarow.
                # Generate synthetic data
                self.logger.info("Generating synthetic data...")
                generation_results = self._generate(input_data=dummy_input, num_samples=1000)
                if generation_results['status'] != 'success':
                    return OutputSchema(status='failed', error=generation_results.get('error'))

                self.logger.info("Generation completed successfully")
                return OutputSchema(
                    status='success',
                    generation_results=generation_results,
                    csv_output_results=csv_save_results
                )


        except Exception as e:
            self.logger.error(f"Pipeline failed: {str(e)}")
            return OutputSchema(status='failed', error=str(e))

