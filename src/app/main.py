import logging
from typing import Dict, Any, Optional
import pandas as pd
import os

from src.models.models import PayloadSchema, OutputSchema
from .components.logger import CustomFormatter
from .components.input import InputComponent
from .components.training import TrainingComponent
from .components.inference import InferenceComponent
from .components.evaluation import EvaluationComponent
from .components.payload import Payload



class SyntheticData:
    """
    Main class for synthetic data generation pipeline.
    """

    def __str__(self) -> str:
        return "SYNTHETIC-DATA"

    def __init__(self) -> None:
        """Initialize the SyntheticData class

        Args:
            payload (PayloadSchema): validated payload
        """
        self.logger = logging.getLogger(str(self))
        self._initialize_logging()

    def _initialize_logging(self) -> None:
        """Initialize logging configuration"""
        # Configure logger
        handler = logging.StreamHandler()
        handler.setFormatter(CustomFormatter())
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def _train(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run the training process using database extraction

        Args:
            input_data (Dict[str, Any]): Input data for training

        Returns:
            Dict[str, Any]: Training results
        """

        self.logger.info("Initializing TrainingComponent...")
        training_component = TrainingComponent(payload=self.payload, input_data=input_data)
        self.logger.info("Running training with joint_training=True...")
        result = training_component.run_training(joint_training=True)
        self.logger.info("Training component finished")
        return result

    def _generate(self, input_data: Dict[str, Any], num_samples: int = 1000) -> Dict[str, Any]:
        """Run the data generation process

        Args:
            input_data (Dict[str, Any]): Input data for generation
            num_samples (int, optional): Number of samples to generate

        Returns:
            Dict[str, Any]: Generation results
        """
        self.logger.info(f"Starting data generation for {num_samples} samples...")
        return InferenceComponent(payload=self.payload, input_data=input_data).run_generation(
            num_samples=num_samples,
            joint_training=True
        )

    def _evaluate(self, synthetic_data: Dict[str, Any], real_data: Optional[Dict[str, Any]] = None, dataframes: Optional[list] = None) -> Dict[str, Any]:
        """Run the evaluation process

        Args:
            synthetic_data: Generated synthetic data
            real_data: Original real data for comparison
            dataframes: List of dataframes to evaluate (for local evaluation)

        Returns:
            Dict[str, Any]: Evaluation results
        """

        return EvaluationComponent(
            payload=self.payload,
            synthetic_data=synthetic_data,
            real_data=real_data,
            dataframes=dataframes
        ).run_evaluation()

    def main(self, payload: PayloadSchema, quick_mode: bool = False) -> OutputSchema:
        """Main method of the SyntheticData class

        Returns:
            OutputSchema: output of the synthetic data generation
        """
        try:
            self.logger.info("Starting synthetic data pipeline")

            # Initialize and validate payload
            self.logger.info("Validating payload...")
            self.payload = Payload.validated_payload(payload)
            self.logger.info(f"Payload validated successfully: {self.payload.run_type}")

            # Training Only Pipeline
            if self.payload.run_type == 'training':
                self.logger.info("Running TRAINING ONLY pipeline")

                # Extract training data
                self.logger.info("Initializing InputComponent...")
                input_component = InputComponent(payload=self.payload)
                self.logger.info("Loading training data...")
                input_data = input_component.load_training_data()
                self.logger.info("Training data loaded successfully")

                # Run training pipeline
                self.logger.info("Starting training pipeline...")
                training_results = self._train(input_data=input_data)
                self.logger.info("Training pipeline completed")

                if training_results['status'] != 'success':
                    self.logger.error("Training failed")
                    return OutputSchema(status='failed', error=training_results.get('error'))

                self.logger.info("Training completed successfully")
                return OutputSchema(
                    status='success',
                    training_results=training_results
                )

            # Generation Only Pipeline
            elif self.payload.run_type == 'generation':
                self.logger.info("Running GENERATION ONLY pipeline")

                # Check if models exist
                if not (os.path.exists("models_local/multi_table_gan.pth") and
                       os.path.exists("models_local/preprocessor.pkl")):
                    return OutputSchema(status='failed', error="No trained models found. Run training first.")

                # Load inference data for generation
                input_data = InputComponent(payload=self.payload).load_inference_data()

                # Generate synthetic data
                self.logger.info("Generating synthetic data...")
                generation_results = self._generate(input_data=input_data, num_samples=1000)
                if generation_results['status'] != 'success':
                    return OutputSchema(status='failed', error=generation_results.get('error'))

                # Save CSV files
                self.logger.info("Saving synthetic CSV files...")
                inference_component = InferenceComponent(payload=self.payload, input_data=input_data)
                csv_save_results = inference_component.save_synthetic_csvs(generation_results['generated_data'])

                self.logger.info("Generation completed successfully")
                return OutputSchema(
                    status='success',
                    generation_results=generation_results,
                    csv_output_results=csv_save_results
                )

            # Full Pipeline (Training + Generation + Evaluation)
            elif self.payload.run_type == 'full_pipeline':
                self.logger.info("Running FULL PIPELINE (Training + Generation + Evaluation)")

                # Extract training data
                input_component = InputComponent(payload=self.payload)
                input_data = input_component.load_training_data()

                # Train
                training_results = self._train(input_data=input_data)
                if training_results['status'] != 'success':
                    return OutputSchema(status='failed', error=training_results.get('error'))

                # Generate
                generation_results = self._generate(input_data=input_data, num_samples=1000)
                if generation_results['status'] != 'success':
                    return OutputSchema(status='failed', error=generation_results.get('error'))

                # Evaluate
                evaluation_results = self._evaluate(
                    synthetic_data=generation_results['generated_data'],
                    real_data=input_data['data'],
                    dataframes=[input_data['data']['main'], input_data['data']['performance'], input_data['data']['conversions']]
                )

                # Save CSVs
                inference_component = InferenceComponent(payload=self.payload, input_data=input_data)
                csv_save_results = inference_component.save_synthetic_csvs(generation_results['generated_data'])

                return OutputSchema(
                    status='success',
                    training_results=training_results,
                    generation_results=generation_results,
                    evaluation_results=evaluation_results,
                    csv_output_results=csv_save_results
                )

            if self.payload.run_type == 'inference':
                # Import inference data
                input_data = InputComponent(payload=self.payload).load_inference_data()

                # Validate input_data structure for inference
                if not isinstance(input_data, dict) or 'data' not in input_data or not isinstance(input_data['data'], dict):
                    self.logger.error("Input data for inference is malformed: expected a dict with a 'data' key containing a dict of DataFrames.")
                    return OutputSchema(status='failed', error="Input data for inference is malformed: expected a dict with a 'data' key containing a dict of DataFrames.")

                # Generate synthetic data
                inference_results = self._generate(input_data=input_data)
                if inference_results['status'] != 'success':
                    self.logger.error("Inference failed, stopping pipeline")
                    return OutputSchema(status='failed', error=inference_results.get('error'))

                # Run evaluation for inference mode too (so demo gets eval results)
                self.logger.info("Running evaluation for inference mode...")
                try:
                    evaluation_results = self._evaluate(
                        synthetic_data=inference_results['generated_data'],
                        real_data=input_data['data'],
                        dataframes=[input_data['data']['main'], input_data['data']['performance'], input_data['data']['conversions']]
                    )
                    self.logger.info("Evaluation completed for inference mode")
                except Exception as e:
                    self.logger.warning(f"Evaluation had issues in inference mode: {str(e)[:100]}...")
                    evaluation_results = {
                        'status': 'completed_with_warnings',
                        'message': 'Evaluation ran but had data processing issues',
                        'error_summary': str(e)[:200]
                    }

                # Save CSVs for inference mode
                inference_component = InferenceComponent(payload=self.payload, input_data=input_data)
                inference_component.save_synthetic_csvs(inference_results['generated_data'])

                self.logger.info("Synthetic data pipeline completed successfully")

                return OutputSchema(
                    status='success',
                    generation_results=inference_results,
                    evaluation_results=evaluation_results  # Now includes evaluation results!
                )

        except Exception as e:
            self.logger.error(f"Pipeline failed: {str(e)}")
            return OutputSchema(status='failed', error=str(e))

