import json
import logging
import os
import pickle
from typing import Any, Dict

import numpy as np
import pandas as pd
import torch

from src.config import settings
from src.gan.conditional_gan import ConditionalGAN
from src.generation.generate import DataGenerator
from src.models.models import PayloadSchema


class InferenceComponent:
    """
    Component responsible for orchestrating the data generation/inference process.
    """

    def __str__(self) -> str:
        return "INFERENCE-COMPONENT"

    def __init__(self, payload: PayloadSchema, input_data: Dict[str,Any]):
        """
        Initialize the InferenceComponent.
        """
        self.logger = logging.getLogger(str(self))
        self.payload = payload
        self.input_data = input_data

    def _is_empty_dataframe(self, df):
        """Utility to check if a DataFrame is empty or None."""
        return df is None or (isinstance(df, pd.DataFrame) and df.empty)

    def run_generation(self, num_samples: int = 1000, joint_training: bool = True) -> Dict[str, Any]:
        """
        Orchestrate the complete data generation process.

        Args:
            num_samples (int): Number of samples to generate
            joint_training (bool): Whether to use joint training models

        Returns:
            Dict[str, Any]: Generation results
        """
        try:
            self.logger.info(f"Starting data generation for {num_samples} samples...")

            # Extract real data from input_data if available for pattern analysis
            real_data = None
            if self.input_data and 'data' in self.input_data:
                data_dict = self.input_data['data']
                if isinstance(data_dict, dict):
                    # Convert to the expected format for pattern analysis
                    real_data = {}

                    # Handle both training data format and inference data format
                    if 'main' in data_dict:
                        real_data['main'] = data_dict['main']
                    elif 'media' in data_dict:
                        real_data['main'] = data_dict['media']
                    if 'performance' in data_dict:
                        real_data['performance'] = data_dict['performance']
                    if 'conversions' in data_dict:
                        real_data['conversions'] = data_dict['conversions']
                    elif 'performance_conversions' in data_dict:
                        real_data['conversions'] = data_dict['performance_conversions']

                    if real_data:
                        self.logger.info(f"Real data available for pattern analysis: {list(real_data.keys())}")
                        # Check for empty DataFrames
                        for k, v in real_data.items():
                            if self._is_empty_dataframe(v):
                                self.logger.error(f"Input data for '{k}' is empty. Cannot proceed with generation.")
                                return {
                                    'generated_data': None,
                                    'status': 'failed',
                                    'error': f"Input data for '{k}' is empty. Please check your input or SQL query."
                                }
                    else:
                        self.logger.warning("No compatible real data found for pattern analysis")
                        return {
                            'generated_data': None,
                            'status': 'failed',
                            'error': 'No compatible real data found for pattern analysis.'
                        }
                else:
                    self.logger.error("Input data 'data' field is not a dict.")
                    return {
                        'generated_data': None,
                        'status': 'failed',
                        'error': "Input data 'data' field is not a dict."
                    }
            else:
                self.logger.error("No input data provided for inference.")
                return {
                    'generated_data': None,
                    'status': 'failed',
                    'error': 'No input data provided for inference.'
                }

            # Initialize generator with real data for pattern analysis
            generator = DataGenerator(
                num_samples=num_samples,
                joint_training=joint_training,
                real_data=real_data
            )

            # Generate synthetic data (but don't save CSVs yet)
            generated_data = generator.generate()

            if generated_data:
                self.logger.info("Data generation completed - ready for evaluation")

                return {
                    'generated_data': generated_data,
                    'output_dir': settings.PATHS['OUTPUT_DIR'],
                    'num_samples': num_samples,
                    'status': 'success'
                }
            else:
                self.logger.error("Data generation failed - no data generated")
                return {
                    'generated_data': None,
                    'status': 'failed',
                    'error': 'No data generated'
                }

        except Exception as e:
            self.logger.error(f"Data generation failed: {str(e)}")
            return {
                'generated_data': None,
                'status': 'failed',
                'error': str(e)
            }

    def save_synthetic_csvs(self, generated_data: Dict[str, Any]) -> Dict[str, Any]:
        """Save synthetic data to CSV files after evaluation."""
        try:
            # Simple CSV saving using existing generator (no real data needed for saving)
            generator = DataGenerator(num_samples=100)  # num_samples doesn't matter for saving
            generator.save_synthetic_data(generated_data, output_dir=settings.PATHS['OUTPUT_DIR'])

            self.logger.info("Synthetic CSV files saved after evaluation")
            return {'status': 'success'}

        except Exception as e:
            self.logger.error(f"Failed to save CSV files: {str(e)}")
            return {'status': 'failed', 'error': str(e)}

    def run_conditional_generation(self, num_samples: int = 1000) -> Dict[str, Any]:
        """
        Run conditional generation where media dimension generates performance and conversions

        Args:
            num_samples: Number of samples to generate

        Returns:
            Dict[str, Any]: Generation results
        """
        try:
            self.logger.info(f"Starting conditional generation for {num_samples} samples...")
            self.logger.info("Architecture: Media Dimension → Performance + Conversions")

            # Load conditional model

            model_path = os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "conditional_gan.pth")
            if not os.path.exists(model_path):
                return {
                    'status': 'failed',
                    'error': 'Conditional model not found. Run conditional training first.'
                }

            # Get media dimension data for conditioning
            if 'data' in self.input_data and 'main' in self.input_data['data']:
                media_data = self.input_data['data']['main']
            else:
                # Create dummy media data if not available
                media_data = pd.DataFrame({
                    'client_id': range(1, num_samples + 1),
                    'campaign_id': range(100, 100 + num_samples),
                    'mediaplan_id': range(1000, 1000 + num_samples)
                })

            # Get numeric columns only (same as training)
            # Force convert all data to numeric, same as training
            media_numeric = media_data.apply(pd.to_numeric, errors='coerce').fillna(0)
            media_numeric = media_numeric.astype(np.float64)

            self.logger.info(f"Media data converted to numeric: {media_numeric.shape}")

            # Load model dimensions from saved dimensions file
            dimensions_path = os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "conditional_dimensions.json")
            try:
                self.logger.info(f"Attempting to load dimensions from {dimensions_path}")
                
                if os.path.exists(dimensions_path):
                    with open(dimensions_path, 'r') as f:
                        dimensions = json.load(f)
                    
                    # Verify dimensions are present and valid
                    if all(k in dimensions for k in ['media_dim', 'perf_dim', 'conv_dim']):
                        media_dim = dimensions['media_dim']
                        perf_dim = dimensions['perf_dim']
                        conv_dim = dimensions['conv_dim']
                        self.logger.info(f"Loaded model dimensions: {dimensions}")
                    else:
                        raise ValueError("Dimensions file missing required keys")
                else:
                    raise FileNotFoundError(f"Dimensions file not found at {dimensions_path}")
            except Exception as e:
                self.logger.warning(f"Could not load dimensions file: {e}")
                # Fallback to estimating from data
                media_dim = media_numeric.shape[1]
                perf_dim = 22  # Updated fallback estimate based on error message
                conv_dim = 10  # Fallback estimate
                
                # Create dimensions file for future use
                try:
                    os.makedirs(os.path.dirname(dimensions_path), exist_ok=True)
                    with open(dimensions_path, 'w') as f:
                        json.dump({
                            'media_dim': media_dim,
                            'perf_dim': perf_dim,
                            'conv_dim': conv_dim
                        }, f)
                    self.logger.info(f"Created new dimensions file with detected values")
                except Exception as write_err:
                    self.logger.warning(f"Could not create dimensions file: {write_err}")
                
                self.logger.warning(f"Using estimated dimensions: media={media_dim}, perf={perf_dim}, conv={conv_dim}")

            device = settings.TRAINING_PARAMS['device']
            model = ConditionalGAN(
                media_dim=media_dim,
                perf_dim=perf_dim,
                conv_dim=conv_dim,
                device=device
            )

            # Load trained weights
            model.load_state_dict(torch.load(model_path, map_location=device))
            model.eval()

            # Prepare media data tensor using numeric data
            media_tensor = torch.FloatTensor(media_numeric.values[:num_samples]).to(device)
            
            # Debug information
            self.logger.info(f"Media tensor shape: {media_tensor.shape}, expected media_dim: {media_dim}")
            
            # Ensure media tensor has the correct number of features
            if media_tensor.shape[1] != media_dim:
                self.logger.warning(f"Media tensor shape mismatch: got {media_tensor.shape[1]} features but model expects {media_dim}")
                if media_tensor.shape[1] < media_dim:
                    # Pad with zeros if we have fewer features than expected
                    padding = torch.zeros(media_tensor.shape[0], media_dim - media_tensor.shape[1], device=device)
                    media_tensor = torch.cat([media_tensor, padding], dim=1)
                else:
                    # Truncate if we have more features than expected
                    media_tensor = media_tensor[:, :media_dim]
                self.logger.info(f"Adjusted media tensor shape: {media_tensor.shape}")
            
            # Generate performance and conversions
            with torch.no_grad():
                perf_data, conv_data = model.generate(media_tensor, num_samples)

            # Load preprocessor to get proper column names and apply inverse transform
            preprocessor_path = os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "preprocessor.pkl")

            if os.path.exists(preprocessor_path):
                self.logger.info("Loading preprocessor for proper column names and inverse transform")
                with open(preprocessor_path, 'rb') as f:
                    preprocessor = pickle.load(f)

                # Get original column names from preprocessor
                if hasattr(preprocessor, 'feature_names_') and hasattr(preprocessor, 'target_names_'):
                    # Use preprocessor column names
                    main_columns = preprocessor.feature_names_.get('main', [f'main_col_{i}' for i in range(media_data.shape[1])])
                    perf_columns = preprocessor.target_names_.get('performance', [f'perf_col_{i}' for i in range(perf_data.shape[1])])
                    conv_columns = preprocessor.target_names_.get('conversions', [f'conv_col_{i}' for i in range(conv_data.shape[1])])
                else:
                    # Fallback to generic names
                    main_columns = [f'main_col_{i}' for i in range(media_data.shape[1])]
                    perf_columns = [f'perf_col_{i}' for i in range(perf_data.shape[1])]
                    conv_columns = [f'conv_col_{i}' for i in range(conv_data.shape[1])]

                # Create DataFrames with proper column names
                # Use the original media_data to preserve all columns
                self.logger.info(f"Original media_data shape: {media_data.shape}")
                
                # Make sure we're using all columns from the original media_data
                main_df = media_data.copy()
                
                # Ensure we have the right number of rows
                if len(main_df) > num_samples:
                    main_df = main_df.head(num_samples)
                elif len(main_df) < num_samples:
                    # If we need more rows, repeat the data
                    repeats_needed = (num_samples + len(main_df) - 1) // len(main_df)
                    main_df = pd.concat([main_df] * repeats_needed).head(num_samples).reset_index(drop=True)
                
                self.logger.info(f"Final main_df shape: {main_df.shape} with columns: {list(main_df.columns)}")
                
                # Create performance and conversions DataFrames
                perf_df = pd.DataFrame(perf_data.cpu().numpy(), columns=perf_columns)
                conv_df = pd.DataFrame(conv_data.cpu().numpy(), columns=conv_columns)
                
                self.logger.info(f"Created DataFrames - main: {main_df.shape}, perf: {perf_df.shape}, conv: {conv_df.shape}")

                # Apply inverse transform if available
                try:
                    if hasattr(preprocessor, 'inverse_transform'):
                        self.logger.info("Applying inverse transform to conditional generated data")
                        generated_data_dict = {
                            'main': main_df,
                            'performance': perf_df,
                            'conversions': conv_df
                        }
                        # Apply inverse transform
                        generated_data_dict = preprocessor.inverse_transform(generated_data_dict)
                        generated_data = generated_data_dict
                    else:
                        generated_data = {
                            'main': main_df,
                            'performance': perf_df,
                            'conversions': conv_df
                        }
                        self.logger.warning("Preprocessor does not have inverse_transform method")
                except Exception as e:
                    self.logger.warning(f"Inverse transform failed: {e}, using raw generated data")
                    generated_data = {
                        'main': main_df,
                        'performance': perf_df,
                        'conversions': conv_df
                    }
            else:
                self.logger.warning("No preprocessor found, using generic column names")
                # Fallback to generic column names
                perf_columns = [f'perf_col_{i}' for i in range(perf_data.shape[1])]
                conv_columns = [f'conv_col_{i}' for i in range(conv_data.shape[1])]

                # Create performance and conversions DataFrames
                perf_df = pd.DataFrame(perf_data.cpu().numpy(), columns=perf_columns)
                conv_df = pd.DataFrame(conv_data.cpu().numpy(), columns=conv_columns)
                
                # Use the original media_data to preserve all columns
                self.logger.info(f"Original media_data shape: {media_data.shape}")
                main_df = media_data.copy()
                
                # Ensure we have the right number of rows
                if len(main_df) > num_samples:
                    main_df = main_df.head(num_samples)
                elif len(main_df) < num_samples:
                    # If we need more rows, repeat the data
                    repeats_needed = (num_samples + len(main_df) - 1) // len(main_df)
                    main_df = pd.concat([main_df] * repeats_needed).head(num_samples).reset_index(drop=True)
                
                self.logger.info(f"Final main_df shape: {main_df.shape} with columns: {list(main_df.columns)}")
                self.logger.info(f"Created DataFrames - main: {main_df.shape}, perf: {perf_df.shape}, conv: {conv_df.shape}")

                generated_data = {
                    'main': main_df,
                    'performance': perf_df,
                    'conversions': conv_df
                }

            self.logger.info("Conditional generation completed successfully!")
            return {
                'generated_data': generated_data,
                'status': 'success',
                'generation_type': 'conditional',
                'num_samples': num_samples
            }

        except Exception as e:
            self.logger.error(f"Conditional generation failed: {str(e)}")
            return {'status': 'failed', 'error': str(e)}