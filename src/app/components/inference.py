import logging
from typing import Dict, Any
from src.config import settings
from src.generation.generate import DataGenerator
from src.models.models import PayloadSchema


class InferenceComponent:
    """
    Component responsible for orchestrating the data generation/inference process.
    """

    def __str__(self) -> str:
        return "INFERENCE-COMPONENT"

    def __init__(self, payload: PayloadSchema, input_data: Dict[str,Any]):
        """
        Initialize the InferenceComponent.
        """
        self.logger = logging.getLogger(str(self))
        self.payload = payload
        self.input_data = input_data

    def _is_empty_dataframe(self, df):
        """Utility to check if a DataFrame is empty or None."""
        try:
            import pandas as pd
            return df is None or (isinstance(df, pd.DataFrame) and df.empty)
        except ImportError:
            return df is None

    def run_generation(self, num_samples: int = 1000, joint_training: bool = True) -> Dict[str, Any]:
        """
        Orchestrate the complete data generation process.

        Args:
            num_samples (int): Number of samples to generate
            joint_training (bool): Whether to use joint training models

        Returns:
            Dict[str, Any]: Generation results
        """
        try:
            self.logger.info(f"Starting data generation for {num_samples} samples...")

            # Extract real data from input_data if available for pattern analysis
            real_data = None
            if self.input_data and 'data' in self.input_data:
                data_dict = self.input_data['data']
                if isinstance(data_dict, dict):
                    # Convert to the expected format for pattern analysis
                    real_data = {}

                    # Handle both training data format and inference data format
                    if 'main' in data_dict:
                        real_data['main'] = data_dict['main']
                    elif 'media' in data_dict:
                        real_data['main'] = data_dict['media']
                    if 'performance' in data_dict:
                        real_data['performance'] = data_dict['performance']
                    if 'conversions' in data_dict:
                        real_data['conversions'] = data_dict['conversions']
                    elif 'performance_conversions' in data_dict:
                        real_data['conversions'] = data_dict['performance_conversions']

                    if real_data:
                        self.logger.info(f"Real data available for pattern analysis: {list(real_data.keys())}")
                        # Check for empty DataFrames
                        for k, v in real_data.items():
                            if self._is_empty_dataframe(v):
                                self.logger.error(f"Input data for '{k}' is empty. Cannot proceed with generation.")
                                return {
                                    'generated_data': None,
                                    'status': 'failed',
                                    'error': f"Input data for '{k}' is empty. Please check your input or SQL query."
                                }
                    else:
                        self.logger.warning("No compatible real data found for pattern analysis")
                        return {
                            'generated_data': None,
                            'status': 'failed',
                            'error': 'No compatible real data found for pattern analysis.'
                        }
                else:
                    self.logger.error("Input data 'data' field is not a dict.")
                    return {
                        'generated_data': None,
                        'status': 'failed',
                        'error': "Input data 'data' field is not a dict."
                    }
            else:
                self.logger.error("No input data provided for inference.")
                return {
                    'generated_data': None,
                    'status': 'failed',
                    'error': 'No input data provided for inference.'
                }

            # Initialize generator with real data for pattern analysis
            generator = DataGenerator(
                num_samples=num_samples,
                joint_training=joint_training,
                real_data=real_data
            )

            # Generate synthetic data (but don't save CSVs yet)
            generated_data = generator.generate()

            if generated_data:
                self.logger.info("Data generation completed - ready for evaluation")

                return {
                    'generated_data': generated_data,
                    'output_dir': settings.PATHS['OUTPUT_DIR'],
                    'num_samples': num_samples,
                    'status': 'success'
                }
            else:
                self.logger.error("Data generation failed - no data generated")
                return {
                    'generated_data': None,
                    'status': 'failed',
                    'error': 'No data generated'
                }

        except Exception as e:
            self.logger.error(f"Data generation failed: {str(e)}")
            return {
                'generated_data': None,
                'status': 'failed',
                'error': str(e)
            }

    def save_synthetic_csvs(self, generated_data: Dict[str, Any]) -> Dict[str, Any]:
        """Save synthetic data to CSV files after evaluation."""
        try:
            # Simple CSV saving using existing generator (no real data needed for saving)
            generator = DataGenerator(num_samples=100)  # num_samples doesn't matter for saving
            generator.save_synthetic_data(generated_data, output_dir=settings.PATHS['OUTPUT_DIR'])

            self.logger.info("Synthetic CSV files saved after evaluation")
            return {'status': 'success'}

        except Exception as e:
            self.logger.error(f"Failed to save CSV files: {str(e)}")
            return {'status': 'failed', 'error': str(e)}