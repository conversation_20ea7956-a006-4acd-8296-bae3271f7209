import logging
from typing import Any, Dict

from src.evaluation.main import EnhancedGANEvaluator


class EvaluationComponent:
    """
    Component responsible for orchestrating the evaluation process.
    """

    def __str__(self) -> str:
        return "EVALUATION-COMPONENT"

    def __init__(self, payload=None, synthetic_data=None, real_data=None, dataframes=None):
        """
        Initialize the EvaluationComponent.

        Args:
            payload: Payload containing evaluation parameters
            synthetic_data: Generated synthetic data
            real_data: Original real data for comparison
            dataframes: List of dataframes to evaluate (for local evaluation)
        """
        self.logger = logging.getLogger(str(self))
        self.payload = payload
        self.synthetic_data = synthetic_data
        self.real_data = real_data
        self.dataframes = dataframes

    def run_evaluation(self, fast_mode: bool = True) -> Dict[str, Any]:
        """
        Orchestrate the complete evaluation process.

        Args:
            fast_mode (bool): Whether to use fast mode (sample data, skip plots)

        Returns:
            Dict[str, Any]: Evaluation results
        """
        try:
            self.logger.info("Starting evaluation process...")

            # If we have synthetic and real data provided, use them directly for evaluation
            if self.synthetic_data is not None and self.real_data is not None:
                self.logger.info("Using provided synthetic and real data for evaluation...")

                # Initialize enhanced evaluator with data
                evaluator = EnhancedGANEvaluator(
                    synthetic_data=self.synthetic_data,
                    real_data=self.real_data,
                    use_real_data=True,
                    fast_mode=fast_mode
                )

                # Run evaluation
                results = evaluator.evaluate_all()

                # Check if evaluation had critical failures
                if results.get('evaluation_status') == 'critical_failure':
                    return {
                        'evaluation_results': results,
                        'evaluator_type': 'enhanced_with_data',
                        'status': 'failed',
                        'error': results.get('error', 'Unknown evaluation error')
                    }
                else:
                    # Even if some components failed, consider it successful if we got results
                    return {
                        'evaluation_results': results,
                        'evaluator_type': 'enhanced_with_data',
                        'status': 'success'
                    }

            # If we have dataframes provided, use them for simple evaluation
            elif self.dataframes is not None:
                self.logger.info("Using provided dataframes for basic evaluation...")

                evaluation_results = {
                    'data_quality': {
                        'main_shape': self.dataframes[0].shape if len(self.dataframes) > 0 else None,
                        'performance_shape': self.dataframes[1].shape if len(self.dataframes) > 1 else None,
                        'conversions_shape': self.dataframes[2].shape if len(self.dataframes) > 2 else None,
                    },
                    'evaluation_type': 'dataframes_based',
                    'status': 'success'
                }

                return {
                    'evaluation_results': evaluation_results,
                    'evaluator_type': 'dataframes',
                    'status': 'success'
                }

            # Fallback: No data provided, return basic status
            self.logger.warning("No synthetic or real data provided for evaluation")
            return {
                'evaluation_results': {
                    'message': 'No data provided for evaluation',
                    'status': 'skipped'
                },
                'evaluator_type': 'none',
                'status': 'success'
            }

        except Exception as e:
            self.logger.error(f"Evaluation failed: {str(e)}")
            return {
                'evaluation_results': None,
                'status': 'failed',
                'error': str(e)
            }

    def run_legacy_evaluation(self) -> Dict[str, Any]:
        """
        Run the legacy evaluation for backward compatibility.

        Returns:
            Dict[str, Any]: Evaluation results
        """
        return self.run_evaluation(fast_mode=True)