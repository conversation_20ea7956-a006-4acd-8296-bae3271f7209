import logging
from dataclasses import dataclass
from typing import Any, Dict, Literal, Optional

from src.models.models import PayloadSchema


@dataclass
class Payload:

    run_type: Literal['inference', 'training']
    client_id: Optional[int] = None
    campaign_id: Optional[int] = None
    mediaplan_id: Optional[int] = None

    def __str__(self) -> str:
        return "PAYLOAD"

    def __post_init__(self) -> None:
        """Post initialization processing"""
        self.logger = logging.getLogger(str(self))

        # Run checks on payload
        if self.run_type == "inference":
            if not all(isinstance(x, int) for x in [self.client_id, self.campaign_id, self.mediaplan_id]):
                raise ValueError("For inference run_type, client_id, campaign_id and mediaplan_id should be integers")
        else:
            if any(x is not None for x in [self.client_id, self.campaign_id, self.mediaplan_id]):
                raise ValueError("For training run_type, client_id, campaign_id and mediaplan_id should be None")

        self.logger.info(
            f"""Payload initialized with
                            client_id: {self.client_id}
                            campaign_id: {self.campaign_id}
                            mediaplan_id: {self.mediaplan_id}
                            """
        )

    @classmethod
    def validated_payload(cls, payload: Dict[str, Any]):
        """Factory method to generate class and validate payload

        Args:
            payload (Dict[str, Any]): payload taxonomy

        Returns:
            Payload: Payload class object
        """

        # Validate structure using Pydantic schema
        PayloadSchema(**payload)

        return cls(**payload)