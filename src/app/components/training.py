import json
import logging
import os
import time
from typing import Any, Dict

import pandas as pd
import torch

from src.config import settings
from src.models.models import PayloadSchema
from src.training.train import GANTrainer


class TrainingComponent:
    """
    Component responsible for orchestrating the training process.
    """

    def __str__(self) -> str:
        return "TRAINING-COMPONENT"

    def __init__(self, payload: PayloadSchema, input_data: Dict[str,Any]):
        """
        Initialize the TrainingComponent.
        """
        self.logger = logging.getLogger(str(self))

        self.payload = payload
        self.input_data = input_data

    def run_training(self, joint_training: bool = True) -> Dict[str, Any]:
        """
        Orchestrate the complete training process using preprocessed data.

        Args:
            joint_training (bool): Whether to use joint training

        Returns:
            Dict[str, Any]: Training results including models and preprocessor
        """
        try:
            self.logger.info("Starting training process with preprocessed data...")

            # Extract preprocessed data from input_data
            data = self.input_data['data']
            preprocessor = self.input_data.get('preprocessor')

            # Validate that we have the required data
            if data is None:
                raise ValueError("No data provided for training")
            if not isinstance(data, dict):
                raise ValueError("Data must be a dictionary with table names as keys")
            if 'main' not in data or 'performance' not in data or 'conversions' not in data:
                raise ValueError("Data must contain 'main', 'performance', and 'conversions' tables")

            # Create a simplified trainer that works with preprocessed data
            self.logger.info("Initializing GANTrainer...")
            trainer = GANTrainer()
            self.logger.info("GANTrainer initialized successfully")

            if joint_training:
                self.logger.info("Starting JOINT training of all tables...")
                self.logger.info(f"Data shapes - main: {data['main'].shape}, performance: {data['performance'].shape}, conversions: {data['conversions'].shape}")

                # Add progress indicator for training
                total_samples = data['main'].shape[0] + data['performance'].shape[0] + data['conversions'].shape[0]
                self.logger.info(f"Training on {total_samples:,} total samples across 3 tables")
                self.logger.info(f"Main: {data['main'].shape[0]:,} rows")
                self.logger.info(f"Performance: {data['performance'].shape[0]:,} rows")
                self.logger.info(f"Conversions: {data['conversions'].shape[0]:,} rows")
                self.logger.info(f"Epochs: {settings.TRAINING_PARAMS['epochs']}")
                self.logger.info(f"Device: {settings.TRAINING_PARAMS['device']}")

                start_time = time.time()
                model = trainer.train_joint_gan(
                    data['main'],
                    data['performance'],
                    data['conversions']
                )
                elapsed = time.time() - start_time
                self.logger.info(f"Joint GAN training completed in {elapsed/60:.1f} minutes")
                self.logger.info("Joint GAN training completed")

                # Save model
                os.makedirs(settings.PATHS['MODELS_LOCAL_DIR'], exist_ok=True)
                torch.save(model.state_dict(), settings.PATHS['MODEL_PATH'])

                result_models = model
            else:
                self.logger.info("Training separate GANs for each table...")
                # For separate training, detect categorical columns automatically
                categorical_cols = {
                    'main': [col for col in data['main'].columns if data['main'][col].dtype == 'object'],
                    'performance': [col for col in data['performance'].columns if data['performance'][col].dtype == 'object'],
                    'conversions': [col for col in data['conversions'].columns if data['conversions'][col].dtype == 'object']
                }

                # Train each model and extract just the Generator
                models = [
                    trainer.train_single_gan(data['main'], settings.TRAINING_PARAMS['noise_dim'],
                               settings.TRAINING_PARAMS['epochs'], categorical_cols['main'])[0],
                    trainer.train_single_gan(data['performance'], settings.TRAINING_PARAMS['noise_dim'],
                               settings.TRAINING_PARAMS['epochs'], categorical_cols['performance'])[0],
                    trainer.train_single_gan(data['conversions'], settings.TRAINING_PARAMS['noise_dim'],
                               settings.TRAINING_PARAMS['epochs'], categorical_cols['conversions'])[0]
                ]

                # Save models
                os.makedirs(settings.PATHS['MODELS_LOCAL_DIR'], exist_ok=True)
                torch.save(models[0].state_dict(), os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "main_generator.pth"))
                torch.save(models[1].state_dict(), os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "performance_generator.pth"))
                torch.save(models[2].state_dict(), os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "conversions_generator.pth"))

                result_models = models

            self.logger.info("Training completed successfully")

            return {
                'data': data,  # Return the training data for evaluation
                'models': result_models,
                'preprocessor': preprocessor,
                'status': 'success'
            }

        except Exception as e:
            self.logger.error(f"Training failed: {str(e)}")
            return {
                'data': None,
                'models': None,
                'preprocessor': None,
                'status': 'failed',
                'error': str(e)
            }

    def run_conditional_training(self) -> Dict[str, Any]:
        """
        Run conditional training where media dimension generates performance and conversions

        Returns:
            Dict[str, Any]: Training results
        """
        try:
            self.logger.info("Starting conditional training...")
            self.logger.info("Architecture: Media Dimension → Performance + Conversions")

            # Extract data
            data = self.input_data['data']

            # Create trainer
            trainer = GANTrainer()

            # Train conditional model
            model = trainer.train_conditional_gan(
                data['main'],
                data['performance'],
                data['conversions']
            )

            # Save conditional model and dimensions
            os.makedirs(settings.PATHS['MODELS_LOCAL_DIR'], exist_ok=True)

            # Save model state dict
            torch.save(model.state_dict(), os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "conditional_gan.pth"))

            # Save model dimensions for loading later (use processed numeric data dimensions)
            # Get the actual dimensions used in training (after numeric conversion)
            # We need to process the data the same way as in training to get correct dimensions
            main_numeric = data['main'].apply(pd.to_numeric, errors='coerce').fillna(0)
            perf_numeric = data['performance'].apply(pd.to_numeric, errors='coerce').fillna(0)
            conv_numeric = data['conversions'].apply(pd.to_numeric, errors='coerce').fillna(0)

            dimensions = {
                'media_dim': main_numeric.shape[1],
                'perf_dim': perf_numeric.shape[1],
                'conv_dim': conv_numeric.shape[1]
            }
            with open(os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "conditional_dimensions.json"), 'w') as f:
                json.dump(dimensions, f)

            self.logger.info(f"Saved conditional model with dimensions: {dimensions}")

            self.logger.info("Conditional training completed successfully!")
            return {
                'status': 'success',
                'model_type': 'conditional_gan',
                'architecture': 'media_dimension_to_performance_conversions'
            }

        except Exception as e:
            self.logger.error(f"Conditional training failed: {str(e)}")
            return {'status': 'failed', 'error': str(e)}


