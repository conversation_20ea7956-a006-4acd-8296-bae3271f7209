import logging
import torch
import os
import time
from typing import Dict, Any

from src.training.train import <PERSON>NTrainer
from src.models.models import PayloadSchema
from src.config import settings


class TrainingComponent:
    """
    Component responsible for orchestrating the training process.
    """

    def __str__(self) -> str:
        return "TRAINING-COMPONENT"

    def __init__(self, payload: PayloadSchema, input_data: Dict[str,Any]):
        """
        Initialize the TrainingComponent.
        """
        self.logger = logging.getLogger(str(self))

        self.payload = payload
        self.input_data = input_data

    def run_training(self, joint_training: bool = True) -> Dict[str, Any]:
        """
        Orchestrate the complete training process using preprocessed data.

        Args:
            joint_training (bool): Whether to use joint training

        Returns:
            Dict[str, Any]: Training results including models and preprocessor
        """
        try:
            self.logger.info("Starting training process with preprocessed data...")

            # Extract preprocessed data from input_data
            data = self.input_data['data']
            preprocessor = self.input_data.get('preprocessor')

            # Validate that we have the required data
            if data is None:
                raise ValueError("No data provided for training")
            if not isinstance(data, dict):
                raise ValueError("Data must be a dictionary with table names as keys")
            if 'main' not in data or 'performance' not in data or 'conversions' not in data:
                raise ValueError("Data must contain 'main', 'performance', and 'conversions' tables")

            # Create a simplified trainer that works with preprocessed data
            self.logger.info("Initializing GANTrainer...")
            trainer = GANTrainer()
            self.logger.info("GANTrainer initialized successfully")

            if joint_training:
                self.logger.info("Starting JOINT training of all tables...")
                self.logger.info(f"Data shapes - main: {data['main'].shape}, performance: {data['performance'].shape}, conversions: {data['conversions'].shape}")

                # Add progress indicator for training
                self.logger.info("Starting Joint GAN training...")
                self.logger.info("=" * 50)
                total_samples = data['main'].shape[0] + data['performance'].shape[0] + data['conversions'].shape[0]
                self.logger.info(f"Training on {total_samples:,} total samples across 3 tables:")
                self.logger.info(f"   • Main: {data['main'].shape[0]:,} rows")
                self.logger.info(f"   • Performance: {data['performance'].shape[0]:,} rows")
                self.logger.info(f"   • Conversions: {data['conversions'].shape[0]:,} rows")
                self.logger.info(f"Epochs: {settings.TRAINING_PARAMS['epochs']}")
                self.logger.info(f"Device: {settings.TRAINING_PARAMS['device']}")
                self.logger.info("=" * 50)

                start_time = time.time()
                model = trainer.train_joint_gan(
                    data['main'],
                    data['performance'],
                    data['conversions']
                )
                elapsed = time.time() - start_time

                self.logger.info("=" * 50)
                self.logger.info(f"Joint GAN training completed! ({elapsed/60:.1f} minutes)")
                self.logger.info("=" * 50)
                self.logger.info("Joint GAN training completed")

                # Save model
                os.makedirs(settings.PATHS['MODELS_LOCAL_DIR'], exist_ok=True)
                torch.save(model.state_dict(), settings.PATHS['MODEL_PATH'])

                result_models = model
            else:
                self.logger.info("Training separate GANs for each table...")
                # For separate training, detect categorical columns automatically
                categorical_cols = {
                    'main': [col for col in data['main'].columns if data['main'][col].dtype == 'object'],
                    'performance': [col for col in data['performance'].columns if data['performance'][col].dtype == 'object'],
                    'conversions': [col for col in data['conversions'].columns if data['conversions'][col].dtype == 'object']
                }

                # Train each model and extract just the Generator
                models = [
                    trainer.train_single_gan(data['main'], settings.TRAINING_PARAMS['noise_dim'],
                               settings.TRAINING_PARAMS['epochs'], categorical_cols['main'])[0],
                    trainer.train_single_gan(data['performance'], settings.TRAINING_PARAMS['noise_dim'],
                               settings.TRAINING_PARAMS['epochs'], categorical_cols['performance'])[0],
                    trainer.train_single_gan(data['conversions'], settings.TRAINING_PARAMS['noise_dim'],
                               settings.TRAINING_PARAMS['epochs'], categorical_cols['conversions'])[0]
                ]

                # Save models
                os.makedirs(settings.PATHS['MODELS_LOCAL_DIR'], exist_ok=True)
                torch.save(models[0].state_dict(), os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "main_generator.pth"))
                torch.save(models[1].state_dict(), os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "performance_generator.pth"))
                torch.save(models[2].state_dict(), os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "conversions_generator.pth"))

                result_models = models

            self.logger.info("Training completed successfully")

            return {
                'data': data,  # Return the training data for evaluation
                'models': result_models,
                'preprocessor': preprocessor,
                'status': 'success'
            }

        except Exception as e:
            self.logger.error(f"Training failed: {str(e)}")
            return {
                'data': None,
                'models': None,
                'preprocessor': None,
                'status': 'failed',
                'error': str(e)
            }


