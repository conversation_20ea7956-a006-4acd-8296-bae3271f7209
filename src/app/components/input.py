import logging
import os
import pickle
import time
from typing import Any, Dict

import pandas as pd

from src.config import settings
from src.data.input import InputData
from src.data.pre_process import DataPreprocessor
from src.models.models import PayloadSchema



class InputComponent:
    """
    Component responsible for orchestrating the data input process.
    """

    def __str__(self):
        return "INPUT-COMPONENT"

    def __init__(self, payload: PayloadSchema):
        """
        Initialize the InputComponent.
        """
        self.logger = logging.getLogger(str(self))
        self.payload = payload

    def load_training_data(self) -> Dict[str, Any]:
        """
        Orchestrate the training data loading and preprocessing process from database.

        Returns:
            Dict[str, Any]: Loaded and preprocessed training data results
        """
        try:
            self.logger.info("Loading Training Data")

            # Set pandas options specifically for Streamlit environment
            pd.set_option('future.no_silent_downcasting', True)
            pd.set_option('mode.copy_on_write', True)  # Streamlit-specific

            # Initialize input data handler
            input_data = InputData(payload=self.payload)

            # Check if cached data exists for faster debugging
            cache_file = "data/cache/raw_training_data.pkl"

            if os.path.exists(cache_file):
                self.logger.info("Found cached data! Loading from cache for faster debugging...")
                try:
                    with open(cache_file, 'rb') as f:
                        cached_data = pickle.load(f)
                    main_df = cached_data['main']
                    performance_df = cached_data['performance']
                    conversions_df = cached_data['conversions']
                    self.logger.info(f"Loaded from cache: {main_df.shape[0] + performance_df.shape[0] + conversions_df.shape[0]:,} total rows")
                except Exception as e:
                    self.logger.info(f"Cache loading failed: {e}, falling back to database...")
                    main_df, performance_df, conversions_df = input_data.training_set()
            else:
                self.logger.info("No cache found, loading from database...")
                main_df, performance_df, conversions_df = input_data.training_set()



            self.logger.info(f"Training data loaded successfully:")
            self.logger.info(f"  - Main table: {main_df.shape}")
            self.logger.info(f"  - Performance table: {performance_df.shape}")
            self.logger.info(f"  - Conversions table: {conversions_df.shape}")

            # Cache the raw data for debugging (before preprocessing)
            cache_dir = "data/cache"
            os.makedirs(cache_dir, exist_ok=True)

            cache_file = f"{cache_dir}/raw_training_data.pkl"
            self.logger.info(f"Caching raw data to {cache_file} for faster debugging...")
            with open(cache_file, 'wb') as f:
                pickle.dump({
                    'main': main_df,
                    'performance': performance_df,
                    'conversions': conversions_df
                }, f)
            self.logger.info(f"Raw data cached! Use this for debugging without reloading.")

            # Apply nrows limit if specified
            nrows = settings.TRAINING_PARAMS['nrows']
            if nrows:
                main_df = main_df.head(nrows)
                performance_df = performance_df.head(nrows)
                conversions_df = conversions_df.head(nrows)
                self.logger.info(f"Applied nrows limit of {nrows} to all tables")
                self.logger.info(f"Applied nrows limit: {nrows:,} rows per table")

            # Apply data preprocessing
            self.logger.info("Starting data preprocessing...")
            start_time = time.time()
            preprocessor = DataPreprocessor()

            self.logger.info("Preprocessing tables...")
            try:
                main_df, performance_df, conversions_df = preprocessor.fit_transform(
                    main_df, performance_df, conversions_df
                )
            except Exception as e:
                self.logger.error(f"Data preprocessing failed: {str(e)}")
                raise

            elapsed = time.time() - start_time
            self.logger.info(f"Data preprocessing completed in {elapsed:.1f}s")
            self.logger.info(f"Preprocessed data shapes: main={main_df.shape}, performance={performance_df.shape}, conversions={conversions_df.shape}")

            # Save preprocessor state for later use
            preprocessor.save_state()

            return {
                'data': {
                    'main': main_df,
                    'performance': performance_df,
                    'conversions': conversions_df
                },
                'preprocessor': preprocessor,
                'payload': self.payload,
                'status': 'success'
            }

        except Exception as e:
            self.logger.error(f"Training data loading failed: {str(e)}")
            return {
                'data': None,
                'preprocessor': None,
                'status': 'failed',
                'error': str(e)
            }

    def load_inference_data(self) -> Dict[str, Any]:
        """
        Orchestrate the inference data loading process from database.

        Returns:
            Dict[str, Any]: Loaded inference data results
        """
        try:
            self.logger.info("Loading Inference Data")

            # Initialize input data handler
            input_data = InputData(payload=self.payload)

            # Extract inference data from database
            inference_df = input_data.inference_set()
            self.logger.info(f"Inference data loaded successfully: {inference_df.shape}")

            # Wrap in dict to match training data structure
            return {
                'data': {
                    'main': inference_df,
                    'performance': pd.DataFrame(),
                    'conversions': pd.DataFrame()
                },
                'payload': self.payload,
                'status': 'success'
            }

        except Exception as e:
            self.logger.error(f"Inference data loading failed: {str(e)}")
            return {
                'data': None,
                'status': 'failed',
                'error': str(e)
            }
