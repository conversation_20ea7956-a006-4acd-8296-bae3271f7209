import logging

# Add custom log level for DISPLAY
DISPLAY_LEVEL_NUM = 25
logging.addLevelName(DISPLAY_LEVEL_NUM, "DISPLAY")


def display(self, message, *args, **kwargs):
    if self.isEnabledFor(DISPLAY_LEVEL_NUM):
        self._log(DISPLAY_LEVEL_NUM, message, args, **kwargs)


logging.Logger.display = display


class CustomFormatter(logging.Formatter):
    """Custom colouring for log printing when running locally"""
    
    yellow = "\x1b[33;20m"
    green = "\033[92m"
    red = "\x1b[31;20m"
    bold_red = "\x1b[31;1m"
    white = "\033[97m"
    reset = "\x1b[0m"
    style = "\n%(name)s | %(asctime)s | %(levelname)s: %(message)s"

    FORMATS = {
        logging.INFO: green + style + reset,
        logging.ERROR: red + style + reset,
        logging.WARNING: yellow + style + reset,
        logging.CRITICAL: bold_red + style + reset,
        DISPLAY_LEVEL_NUM: white
        + style
        + reset,  # Custom white format for DISPLAY level
    }

    def format(self, record):
        log_fmt = self.FORMATS.get(record.levelno, self.FORMATS[logging.INFO])
        formatter = logging.Formatter(log_fmt)
        return formatter.format(record)


class TotalLog:
    data = []

    @classmethod
    def add(cls, item):
        cls.data.append(item)
