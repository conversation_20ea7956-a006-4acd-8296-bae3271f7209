import os
import torch
import numpy as np
from typing import Optional
from src.config import settings


def set_seed(seed: Optional[int] = None):
    """Set random seeds for reproducibility."""
    if seed is None:
        seed = settings.TRAINING_PARAMS["seed"]
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False


def save_checkpoint(model: torch.nn.Module, optimizer: torch.optim.Optimizer, epoch: int, loss: float):
    """Save model checkpoint"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss
    }

    os.makedirs(settings.PATHS['MODELS_LOCAL_DIR'], exist_ok=True)
    torch.save(checkpoint, os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], f'model_epoch_{epoch}.pth'))


def load_checkpoint(model: torch.nn.Module, optimizer: torch.optim.Optimizer, checkpoint_path: str):
    """Load model checkpoint"""
    checkpoint = torch.load(checkpoint_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    return checkpoint['epoch'], checkpoint['loss']


def get_device():
    """Get available device (CPU or CUDA)"""
    return torch.device('cuda' if torch.cuda.is_available() else 'cpu')


def create_categorical_mask(columns: list, categorical_columns: list) -> torch.Tensor:
    """
    Create a boolean mask indicating which columns are categorical

    Args:
        columns: List of all column names
        categorical_columns: List of column names that are categorical

    Returns:
        Boolean tensor mask where True indicates categorical columns
    """
    mask = torch.zeros(len(columns), dtype=torch.bool)
    for col in categorical_columns:
        if col in columns:
            mask[columns.index(col)] = True
    return mask
