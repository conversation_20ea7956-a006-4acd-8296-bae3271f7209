from pydantic import BaseModel
from typing import Optional, Literal, List


class PayloadSchema(BaseModel):
    run_type: Literal['inference', 'training', 'generation', 'evaluation', 'full_pipeline', 'conditional_training', 'conditional_generation', 'conditional_pipeline']
    client_id: Optional[int]
    campaign_id: Optional[int]
    mediaplan_id: Optional[int]
    dataframes: Optional[List] = None


class OutputSchema(BaseModel):
    status: str
    training_results: Optional[dict] = None
    generation_results: Optional[dict] = None
    evaluation_results: Optional[dict] = None
    csv_output_results: Optional[dict] = None
    error: Optional[str] = None
