from typing import List, Literal, Optional

from pydantic import BaseModel


class PayloadSchema(BaseModel):
    run_type: Literal['training', 'generation']  # Simplified to main conditional training/generation
    client_id: Optional[int]
    campaign_id: Optional[int]
    mediaplan_id: Optional[int]
    dataframes: Optional[List] = None


class OutputSchema(BaseModel):
    status: str
    training_results: Optional[dict] = None
    generation_results: Optional[dict] = None
    evaluation_results: Optional[dict] = None
    csv_output_results: Optional[dict] = None
    error: Optional[str] = None
