from typing import List, Literal, Optional

from pydantic import BaseModel, Field


class PayloadSchema(BaseModel):
    """Schema for payload data"""
    run_type: str = Field(..., description="Type of run (training, generation, evaluation, full_pipeline)")
    client_id: Optional[str] = Field(None, description="Client ID for filtering data")
    campaign_id: Optional[str] = Field(None, description="Campaign ID for filtering data")
    mediaplan_id: Optional[str] = Field(None, description="Media Plan ID for filtering data")


class OutputSchema(BaseModel):
    status: str
    training_results: Optional[dict] = None
    generation_results: Optional[dict] = None
    evaluation_results: Optional[dict] = None
    csv_output_results: Optional[dict] = None
    error: Optional[str] = None
