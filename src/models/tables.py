from typing import List

from pydantic import BaseModel


class SchemaConverter(BaseModel):
    @staticmethod
    def key_to_join(name: str) -> str:
        if name.lower() == "mediarow_id":
            return "KEY TO JOIN"
        else:
            return ""

    def view_schema(self) -> str:
        active_fields = [
            f"t1.{field_name}"
            for field_name, field_value in self.__dict__.items()
            if isinstance(field_value, (BigInt, Varchar, Date, Double))
            and field_value.active
        ]
        return ", ".join(active_fields)

    def prompt_schema(self) -> str:
        active_fields = [
            f"{field_name} ({field_value.dtype}) {self.key_to_join(field_name)}\n"
            for field_name, field_value in self.__dict__.items()
            if isinstance(field_value, (BigInt, Varchar, Date, Double))
            and field_value.active
        ]

        return " ".join(active_fields)

    def get_inactive_fields(self) -> List[str]:
        return [
            field_name
            for field_name, field_value in self.__dict__.items()
            if isinstance(field_value, (BigInt, Varchar, Date, Double))
            and not field_value.active
        ]


class BigInt(BaseModel):
    dtype: str = "bigint"
    active: bool = True
    description: str = ""


class Varchar(BaseModel):
    dtype: str = "varchar"
    active: bool = True
    description: str = ""


class Date(BaseModel):
    dtype: str = "date"
    active: bool = True
    description: str = ""


class Double(BaseModel):
    dtype: str = "double"
    active: bool = True
    description: str = ""


class MediaDim(SchemaConverter):
    """Data structure for media dim"""

    client_id: BigInt = BigInt(description="unique id of the client")
    campaign_id: BigInt = BigInt(description="unique id of the campaign")
    campaign_name: Varchar = BigInt(description="name of the campaign")
    campaign_currency: Varchar = Varchar(description="currency of the campaign")
    mediarow_currency: Varchar = Varchar(description="currency of the mediarow")
    mediarow_lifetime_budget_in_mediarow_currency: Double = Double(
        description="budget of the mediarow in mediarow specific currency"
    )
    mediarow_lifetime_budget_in_campaign_currency: Double = Double(
        description="budget of the mediarow in campaign specific currency"
    )
    mediaplan_id: BigInt = BigInt(description="unique id of the mediaplan")
    mediaplan_name: Varchar = Varchar(description="name opf the mediaplan")
    mediarow_start_date: Date = Date(description="start date of the mediarow")
    mediarow_end_date: Date = Date(description="end date of the mediarow")
    mediaplan_start_date: Date = Date(description="start date of the mediaplan")
    mediaplan_end_date: Date = Date(description="end date of the mediaplan")
    channel_id: BigInt = BigInt(description="id of the channel")
    channel_name: Varchar = Varchar(description="name of the channel")
    platform_id: BigInt = BigInt(description="id of the platform")
    platform_name: Varchar = Varchar(description="name of the platform")
    mediarow_id: BigInt = BigInt(description="unique id of the mediarow")
    mediarow_name: Varchar = Varchar(description="name of the mediarow")

    class Config:
        protected_namespaces = ()
        extra = "forbid"


class Performance(SchemaConverter):
    """Data structure for performance"""

    client_id: BigInt = BigInt(description="unique id of the client")
    campaign_id: BigInt = BigInt(description="unique id of the campaign")
    mediarow_id: BigInt = BigInt(description="unique id of the mediarow")
    date: Date = Date(description="date column")
    spend_in_mediarow_currency: Double = Double(
        description="budget spent by the mediarow in mediarow specific currency on a specific date"
    )
    spend_in_campaign_currency: Double = Double(
        description="budget spent by the mediarow in campaign specific currency on a specific date"
    )
    impressions: Double = Double(
        description="amount of impressions generated by a mediarow on a specific date"
    )
    clicks: Double = Double(
        description="amount of clicks generated by a mediarow on a specific date"
    )
    views: Double = Double(
        description="amount of views generated by a mediarow on a specific date"
    )

    class Config:
        protected_namespaces = ()
        extra = "forbid"


class PerformanceConversions(SchemaConverter):
    """Data structure for performance conversions"""

    client_id: BigInt = BigInt(description="unique id of the client")
    campaign_id: BigInt = BigInt(description="unique id of the campaign")
    mediarow_id: BigInt = BigInt(description="unique id of the mediarow")
    date: Date = Date(description="date column")
    spend_in_mediarow_currency: Double = Double(
        description="budget spent by the mediarow in mediarow specific currency on a specific date"
    )
    spend_in_campaign_currency: Double = Double(
        description="budget spent by the mediarow in campaign specific currency on a specific date"
    )
    conversion_name: Varchar = Varchar(
        description="custom name of the conversion created by the client"
    )
    conversion_cost_based_name: Varchar = Varchar(
        description="custom name of the cost based metric associated to the custom conversion name"
    )
    conversions: Double = Double(
        description="amount of the specific conversions (which name is defined in the conversion_name column) generated by a mediarow on a specific date"
    )

    class Config:
        protected_namespaces = ()
        extra = "forbid"
