import os
import torch
import torch.optim as optim
import torch.nn as nn
from torch.utils.data import Data<PERSON>oader as TorchDataLoader, TensorDataset
import numpy as np
from typing import Optional, List
import logging
import pandas as pd

from src.gan.multi_table_gan import MultiTableGAN
from src.gan.conditional_gan import ConditionalGAN
from src.gan.networks.generator import Generator
from src.gan.networks.discriminator import Discriminator
from src.config import settings
from src.utils.utils import set_seed, save_checkpoint
from src.data.input import InputData
from src.app.components.payload import Payload



class GANTrainer:
    def __str__(self) -> str:
        return "GAN-TRAINER"

    def __init__(self):
        device_str = settings.TRAINING_PARAMS['device']
        self.device = torch.device(device_str)
        self.batch_size = settings.TRAINING_PARAMS['batch_size']
        self.learning_rate = settings.TRAINING_PARAMS['learning_rate']
        self.noise_dim = settings.TRAINING_PARAMS['noise_dim']
        self.logger = logging.getLogger(str(self))
    def _setup_directories(self):
        """Ensure all required directories exist"""
        os.makedirs(settings.PATHS['MODELS_DIR'], exist_ok=True)
        os.makedirs(settings.PATHS['OUTPUT_DIR'], exist_ok=True)

    def _create_dataset(self, data: np.ndarray, categorical_cols: List[str]) -> TorchDataLoader:
        """Create PyTorch DataLoader from numpy array"""
        try:
            # Convert numpy array to tensor and move to device
            tensor_data = torch.FloatTensor(data).to(self.device)

            # Create mask for categorical columns
            # Create a tensor of the same batch size as the data
            batch_size = tensor_data.shape[0]
            num_features = tensor_data.shape[1]

            # Create a mask tensor of shape (batch_size, num_features) and move to device
            categorical_mask = torch.zeros(batch_size, num_features, dtype=torch.bool).to(self.device)

            # Set the mask for categorical columns
            for col_idx in range(num_features):
                if col_idx < len(categorical_cols) and categorical_cols[col_idx]:
                    categorical_mask[:, col_idx] = True

            dataset = TensorDataset(
                tensor_data,
                categorical_mask
            )
            return TorchDataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        except Exception as e:
            raise RuntimeError(f"Error creating dataset: {str(e)}")

    def _initialize_models(self, data: np.ndarray, noise_dim: int, categorical_cols: Optional[List[str]] = None) -> tuple:
        """Initialize GAN models and optimizers"""
        try:
            # Convert categorical_cols list to tensor if provided
            if categorical_cols is not None:
                # Create a boolean mask tensor and move to device
                categorical_mask = torch.zeros(data.shape[1], dtype=torch.bool, device=self.device)
                for col_idx in range(data.shape[1]):
                    if col_idx < len(categorical_cols) and categorical_cols[col_idx]:
                        categorical_mask[col_idx] = True
            else:
                categorical_mask = None

            # Initialize models and move to device
            G = Generator(noise_dim, data.shape[1], categorical_mask, device=self.device)
            D = Discriminator(data.shape[1]).to(self.device)

            optimizer_G = optim.Adam(G.parameters(), lr=self.learning_rate, betas=(0.5, 0.999))
            optimizer_D = optim.Adam(D.parameters(), lr=self.learning_rate, betas=(0.5, 0.999))

            return G, D, optimizer_G, optimizer_D
        except Exception as e:
            raise RuntimeError(f"Error initializing models: {str(e)}")

    def _train_epoch(self, G: Generator, D: Discriminator,
                    optimizer_G: optim.Optimizer, optimizer_D: optim.Optimizer,
                    dataloader: TorchDataLoader) -> tuple:
        """Train models for one epoch with feature matching loss"""
        try:
            g_losses = []
            d_losses = []

            criterion = nn.BCELoss()
            mse_criterion = nn.MSELoss()
            feature_matching_weight = 0.1

            for batch in dataloader:
                real_data, mask = batch
                real_data = real_data.to(self.device)
                mask = mask.to(self.device)

                # Apply mask to real data - keep only numeric values
                real_data = real_data * ~mask

                # Train Discriminator
                optimizer_D.zero_grad()

                # Real data
                real_labels = torch.ones(real_data.size(0), 1, device=self.device)
                output_real = D(real_data)
                d_loss_real = criterion(output_real, real_labels)

                # Generate fake data with categorical mask
                z = torch.randn(real_data.size(0), self.noise_dim, device=self.device)
                fake_data = G(z)

                # Apply mask to fake data - keep only numeric values
                fake_data = fake_data * ~mask

                fake_labels = torch.zeros(real_data.size(0), 1, device=self.device)
                output_fake = D(fake_data.detach())
                d_loss_fake = criterion(output_fake, fake_labels)

                # Feature matching loss
                with torch.no_grad():
                    real_features = D.feature_extractor(real_data)
                fake_features = D.feature_extractor(fake_data.detach())
                feature_matching_loss = mse_criterion(fake_features, real_features)

                d_loss = d_loss_real + d_loss_fake + feature_matching_weight * feature_matching_loss
                d_loss.backward()
                optimizer_D.step()

                # Train Generator
                optimizer_G.zero_grad()
                output = D(fake_data)
                g_loss = criterion(output, real_labels)

                # Feature matching loss for generator
                fake_features = D.feature_extractor(fake_data)
                feature_matching_loss = mse_criterion(fake_features, real_features)

                total_g_loss = g_loss + feature_matching_weight * feature_matching_loss
                total_g_loss.backward()
                optimizer_G.step()

                d_losses.append(d_loss.item())
                g_losses.append(total_g_loss.item())

            return np.mean(d_losses), np.mean(g_losses)

        except Exception as e:
            raise RuntimeError(f"Error during training epoch: {str(e)}")

    def train_single_gan(self, data: pd.DataFrame, noise_dim: int,
                        epochs: int, categorical_cols: Optional[List[str]] = None) -> tuple:
        """Train GAN on a single table's data"""
        try:
            # Create dataset
            dataloader = self._create_dataset(data.values, categorical_cols)

            # Initialize models
            G, D, optimizer_G, optimizer_D = self._initialize_models(data.values, noise_dim, categorical_cols)

            # Training loop
            for epoch in range(epochs):
                d_loss, g_loss = self._train_epoch(G, D, optimizer_G, optimizer_D, dataloader)

                self.logger.info(f'Epoch [{epoch+1}/{epochs}] | '
                           f'D_loss: {d_loss:.4f} | '
                           f'G_loss: {g_loss:.4f}')

                # Save checkpoint every 10 epochs
                if epoch % settings.TRAINING_PARAMS['save_every'] == 0:
                    save_checkpoint(G, optimizer_G, epoch, g_loss)
                    save_checkpoint(D, optimizer_D, epoch, d_loss)

            return G, D

        except Exception as e:
            self.logger.error(f"Training failed: {str(e)}")
            raise

    def train_joint_gan(self, main_df: pd.DataFrame, perf_df: pd.DataFrame, conv_df: pd.DataFrame):
        """Train multi-table GAN with categorical columns support"""
        try:
            # Track categorical columns (simplified detection)
            categorical_columns = {
                'main': [col for col in main_df.columns
                        if isinstance(main_df[col].dtype, pd.CategoricalDtype)
                        or pd.api.types.is_object_dtype(main_df[col])],
                'performance': [col for col in perf_df.columns
                               if isinstance(perf_df[col].dtype, pd.CategoricalDtype)
                               or pd.api.types.is_object_dtype(perf_df[col])],
                'conversions': [col for col in conv_df.columns
                              if isinstance(conv_df[col].dtype, pd.CategoricalDtype)
                              or pd.api.types.is_object_dtype(conv_df[col])]
            }

            # Ensure all data is numeric before creating tensors
            self.logger.info("Converting data to numeric format...")

            # Convert to numeric, replacing any non-numeric values with 0
            main_numeric = pd.DataFrame(main_df).apply(pd.to_numeric, errors='coerce').fillna(0).astype(np.float32)
            perf_numeric = pd.DataFrame(perf_df).apply(pd.to_numeric, errors='coerce').fillna(0).astype(np.float32)
            conv_numeric = pd.DataFrame(conv_df).apply(pd.to_numeric, errors='coerce').fillna(0).astype(np.float32)

            self.logger.info(f"Data types after conversion:")
            self.logger.info(f"  Main: {main_numeric.dtypes.unique()}")
            self.logger.info(f"  Performance: {perf_numeric.dtypes.unique()}")
            self.logger.info(f"  Conversions: {conv_numeric.dtypes.unique()}")

            # Create dataloaders with consistent batch sizes
            main_loader = TorchDataLoader(
                TensorDataset(torch.FloatTensor(main_numeric.values)),
                batch_size=self.batch_size,
                shuffle=True,
                drop_last=True  # Drop last incomplete batch
            )
            perf_loader = TorchDataLoader(
                TensorDataset(torch.FloatTensor(perf_numeric.values)),
                batch_size=self.batch_size,
                shuffle=True,
                drop_last=True  # Drop last incomplete batch
            )
            conv_loader = TorchDataLoader(
                TensorDataset(torch.FloatTensor(conv_numeric.values)),
                batch_size=self.batch_size,
                shuffle=True,
                drop_last=True  # Drop last incomplete batch
            )

            # Convert categorical columns to boolean masks
            main_mask = [col in categorical_columns['main'] for col in main_numeric.columns]
            perf_mask = [col in categorical_columns['performance'] for col in perf_numeric.columns]
            conv_mask = [col in categorical_columns['conversions'] for col in conv_numeric.columns]

            # Initialize model with categorical columns
            mt_gan = MultiTableGAN(
                noise_dim=self.noise_dim,
                main_dim=main_numeric.shape[1],
                perf_dim=perf_numeric.shape[1],
                conv_dim=conv_numeric.shape[1],
                categorical_cols_main=main_mask,
                categorical_cols_perf=perf_mask,
                categorical_cols_conv=conv_mask,
                learning_rate=self.learning_rate,
                device=self.device
            )

            for epoch in range(settings.TRAINING_PARAMS['epochs']):
                d_losses = []
                g_losses = []

                # Initialize iterators
                main_iter = iter(main_loader)
                perf_iter = iter(perf_loader)
                conv_iter = iter(conv_loader)

                # Training loop
                for i in range(len(main_loader)):
                    try:
                        # Get real data from all tables
                        real_main = next(main_iter)[0]
                        real_perf = next(perf_iter)[0]
                        real_conv = next(conv_iter)[0]

                        # Train step
                        d_loss, g_loss = mt_gan.train_step(real_main, real_perf, real_conv)

                        # Skip if we got NaN losses (error in train_step)
                        if not np.isnan(d_loss) and not np.isnan(g_loss):
                            d_losses.append(d_loss)
                            g_losses.append(g_loss)

                            if i % 100 == 0:
                                self.logger.info(f"Epoch [{epoch}/{settings.TRAINING_PARAMS['epochs']}] "
                                          f"Batch [{i}/{len(main_loader)}] "
                                          f"G_Loss: {g_loss:.4f} "
                                          f"D_Loss: {d_loss:.4f}")
                    except StopIteration:
                        break
                    except Exception as e:
                        self.logger.error(f"Error in training batch: {str(e)}")
                        break

                # Calculate average losses for the epoch
                if d_losses and g_losses:  # Only calculate if we have losses
                    avg_d_loss = sum(d_losses) / len(d_losses)
                    avg_g_loss = sum(g_losses) / len(g_losses)
                    self.logger.info(f"Epoch [{epoch}/{settings.TRAINING_PARAMS['epochs']}] "
                              f"Avg D_Loss: {avg_d_loss:.4f} "
                              f"Avg G_Loss: {avg_g_loss:.4f}")
                else:
                    self.logger.warning(f"Epoch [{epoch}/{settings.TRAINING_PARAMS['epochs']}] "
                                 f"No valid losses recorded")

                # Save checkpoint
                if epoch % settings.TRAINING_PARAMS['save_every'] == 0 and d_losses and g_losses:
                    save_checkpoint(mt_gan, mt_gan.optimizer_G, epoch, g_loss)
                    save_checkpoint(mt_gan, mt_gan.optimizer_D, epoch, d_loss)
        except Exception as e:
            self.logger.error(f"Training failed: {str(e)}")
            raise

        return mt_gan

    def train_conditional_gan(self, main_df: pd.DataFrame, performance_df: pd.DataFrame,
                            conversions_df: pd.DataFrame) -> ConditionalGAN:
        """
        Train conditional GAN where media dimension generates performance and conversions

        Args:
            main_df: Media dimension table (input/condition)
            performance_df: Performance table (target)
            conversions_df: Conversions table (target)

        Returns:
            Trained ConditionalGAN model
        """
        try:
            self.logger.info("Starting conditional GAN training...")
            self.logger.info("Media dimension → Performance + Conversions")

            # Get dimensions
            media_dim = main_df.shape[1]
            perf_dim = performance_df.shape[1]
            conv_dim = conversions_df.shape[1]

            # Initialize conditional GAN
            device = settings.TRAINING_PARAMS['device']
            model = ConditionalGAN(
                media_dim=media_dim,
                perf_dim=perf_dim,
                conv_dim=conv_dim,
                noise_dim=settings.TRAINING_PARAMS['noise_dim'],
                device=device
            )

            # Prepare data
            media_tensor = torch.FloatTensor(main_df.values).to(device)
            perf_tensor = torch.FloatTensor(performance_df.values).to(device)
            conv_tensor = torch.FloatTensor(conversions_df.values).to(device)

            # Create dataset (align all tables by taking minimum length)
            min_len = min(len(media_tensor), len(perf_tensor), len(conv_tensor))
            media_tensor = media_tensor[:min_len]
            perf_tensor = perf_tensor[:min_len]
            conv_tensor = conv_tensor[:min_len]

            dataset = TensorDataset(media_tensor, perf_tensor, conv_tensor)
            dataloader = TorchDataLoader(
                dataset,
                batch_size=settings.TRAINING_PARAMS['batch_size'],
                shuffle=True
            )

            # Initialize optimizers
            lr = settings.TRAINING_PARAMS['learning_rate']
            optimizer_G = optim.Adam(model.generator.parameters(), lr=lr, betas=(0.5, 0.999))
            optimizer_D = optim.Adam(model.discriminator.parameters(), lr=lr, betas=(0.5, 0.999))

            # Loss function
            criterion = nn.BCELoss()

            # Training loop
            epochs = settings.TRAINING_PARAMS['epochs']
            for epoch in range(epochs):
                d_losses, g_losses = [], []

                for batch_idx, (media_batch, perf_batch, conv_batch) in enumerate(dataloader):
                    batch_size = media_batch.size(0)

                    # Real and fake labels
                    real_labels = torch.ones(batch_size, 1, device=device)
                    fake_labels = torch.zeros(batch_size, 1, device=device)

                    # Train Discriminator
                    optimizer_D.zero_grad()

                    # Real data
                    real_perf_validity, real_conv_validity = model.discriminator(
                        media_batch, perf_batch, conv_batch
                    )
                    d_real_loss = (criterion(real_perf_validity, real_labels) +
                                 criterion(real_conv_validity, real_labels)) / 2

                    # Fake data
                    noise = torch.randn(batch_size, model.noise_dim, device=device)
                    fake_perf, fake_conv = model.generator(media_batch, noise)
                    fake_perf_validity, fake_conv_validity = model.discriminator(
                        media_batch, fake_perf.detach(), fake_conv.detach()
                    )
                    d_fake_loss = (criterion(fake_perf_validity, fake_labels) +
                                 criterion(fake_conv_validity, fake_labels)) / 2

                    d_loss = d_real_loss + d_fake_loss
                    d_loss.backward()
                    optimizer_D.step()

                    # Train Generator
                    optimizer_G.zero_grad()

                    fake_perf_validity, fake_conv_validity = model.discriminator(
                        media_batch, fake_perf, fake_conv
                    )
                    g_loss = (criterion(fake_perf_validity, real_labels) +
                            criterion(fake_conv_validity, real_labels)) / 2

                    g_loss.backward()
                    optimizer_G.step()

                    d_losses.append(d_loss.item())
                    g_losses.append(g_loss.item())

                # Log progress
                if epoch % 2 == 0:
                    self.logger.info(
                        f"Epoch [{epoch}/{epochs}] "
                        f"D_loss: {np.mean(d_losses):.4f} "
                        f"G_loss: {np.mean(g_losses):.4f}"
                    )

            self.logger.info("Conditional GAN training completed!")
            return model

        except Exception as e:
            self.logger.error(f"Error during conditional GAN training: {str(e)}")
            raise RuntimeError(f"Conditional GAN training failed: {str(e)}")

    def train_gan(self, payload=None, joint_training: bool = False):
        """Main training function using database extraction"""
        set_seed(settings.TRAINING_PARAMS['seed'])

        # Load and preprocess data from database

        # Create payload if not provided
        if payload is None:
            payload = Payload()

        # Initialize input data handler and extract from database
        input_data = InputData(payload=payload)
        main_df, performance_df, conversions_df = input_data.training_set()

        self.logger.info(f"Loaded training data from database:")
        self.logger.info(f"  - Main table: {main_df.shape}")
        self.logger.info(f"  - Performance table: {performance_df.shape}")
        self.logger.info(f"  - Conversions table: {conversions_df.shape}")

        # Apply nrows limit if specified
        nrows = settings.TRAINING_PARAMS['nrows']
        if nrows:
            main_df = main_df.head(nrows)
            performance_df = performance_df.head(nrows)
            conversions_df = conversions_df.head(nrows)
            self.logger.info(f"Applied nrows limit of {nrows} to all tables")

        # Note: Data preprocessing is now handled in the input component
        # The data should already be preprocessed when it arrives here

        if joint_training:
            self.logger.info("Starting JOINT training of all tables...")
            model = self.train_joint_gan(main_df, performance_df, conversions_df)

            # Save model
            torch.save(model.state_dict(), settings.PATHS['MODEL_PATH'])
        else:
            self.logger.info("Training separate GANs for each table...")
            # For separate training, we'll detect categorical columns automatically
            # This is a simplified approach since preprocessing is handled elsewhere
            categorical_cols = {
                'main': [col for col in main_df.columns if main_df[col].dtype == 'object'],
                'performance': [col for col in performance_df.columns if performance_df[col].dtype == 'object'],
                'conversions': [col for col in conversions_df.columns if conversions_df[col].dtype == 'object']
            }

            # Train each model and extract just the Generator
            models = [
                self.train_single_gan(main_df, settings.TRAINING_PARAMS['noise_dim'],
                           settings.TRAINING_PARAMS['epochs'], categorical_cols['main'])[0],
                self.train_single_gan(performance_df, settings.TRAINING_PARAMS['noise_dim'],
                           settings.TRAINING_PARAMS['epochs'], categorical_cols['performance'])[0],
                self.train_single_gan(conversions_df, settings.TRAINING_PARAMS['noise_dim'],
                           settings.TRAINING_PARAMS['epochs'], categorical_cols['conversions'])[0]
            ]

            # Save models
            os.makedirs(settings.PATHS['MODELS_LOCAL_DIR'], exist_ok=True)
            torch.save(models[0].state_dict(), os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "main_generator.pth"))
            torch.save(models[1].state_dict(), os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "performance_generator.pth"))
            torch.save(models[2].state_dict(), os.path.join(settings.PATHS['MODELS_LOCAL_DIR'], "conversions_generator.pth"))

            self.logger.info("\nTraining complete! Models saved.")
            return models, None

        self.logger.info("\nTraining complete! Models saved.")
        return model if joint_training else models, None
