from typing import Final
from environs import Env

# Initialize environment variable loader
env = Env()
env.read_env()  # This will read from .env file

# Environment Configuration
ENVIRONMENT: Final = env.str("ENVIRONMENT", "prod")

# AWS Credentials (loaded from .env file)
AWS_ACCESS_KEY_ID: Final = env.str("AWS_ACCESS_KEY_ID", "")
AWS_SECRET_ACCESS_KEY: Final = env.str("AWS_SECRET_ACCESS_KEY", "")
AWS_SESSION_TOKEN: Final = env.str("AWS_SESSION_TOKEN", "")

# Snowflake Configuration (loaded from .env file)
SNOWFLAKE_ACCOUNT: Final = env.str("SNOWFLAKE_ACCOUNT", "")
SNOWFLAKE_USER: Final = env.str("SNOWFLAKE_USER", "")
SNOWFLAKE_PRIVATE_KEY: Final = env.str("SNOWFLAKE_PRIVATE_KEY", "")