from typing import Any

import src.settings as st

# Empty object
EMPTY = object()


class Settings:
    """Class to store all settings in self"""

    def __init__(self):
        """Initializer for Settings class"""
        for name in dir(st):
            if name.isupper():
                setattr(self, name, getattr(st, name))


class LazySettings:
    """Class to create Settings object on first attribute getting"""

    def __init__(self) -> None:
        """Initializer for LazySettings class"""
        self._wrapped = EMPTY

    def _setup(self) -> None:
        """Create Settings object"""
        self._wrapped = Settings()

    def __getattr__(self, name: str) -> Any:
        """Get settings attribute"""
        if self._wrapped is EMPTY:
            self._setup()

        return getattr(self._wrapped, name)


settings = LazySettings()
