import os
import torch
from src.constants import (
    EN<PERSON><PERSON>NMENT,
    SNOW<PERSON>AKE_ACCOUNT,
    SNOWFLAKE_PRIVATE_KEY,
    SNOWFLAKE_USER,
)


# Device selection logic
if torch.cuda.is_available():
    device = "cuda"
elif torch.backends.mps.is_available() and torch.backends.mps.is_built():
    device = "mps"  # Metal for Mac M1/M2
else:
    device = "cpu"



# AWS Athena Configuration
ATHENA = {
    "db_name": f"{ENVIRONMENT.lower()}-datalake",
    "region": "eu-west-1",
    "output_url": f"myn-{ENVIRONMENT}-athena-output",
    "output_folder": "athena",
    "save_results": False,
    "workgroup": "primary",
    "query_path": "src/sql",
    "available_tables": [
        "ds_media_dim_main",
        "ds_performance_main",
        "ds_performance_conversions_main",
        "ds_mediaplan_client_main"
    ],
}

# Snowflake Configuration
SNOWFLAKE = {
    "account": SNOWFLAKE_ACCOUNT,
    "user": SN<PERSON><PERSON>AKE_USER,
    "private_key_file": SN<PERSON><PERSON>AKE_PRIVATE_KEY,
    "warehouse": f"{ENVIRONMENT.upper()}_WAREHOUSE",
    "database": f"{ENVIRONMENT.upper()}_DATAMART",
    "schema": "DATALAKE_SOURCES",
}

# Logger Configuration
LOGGER = {"separator": "-" * 30}



# Training Parameters
TRAINING_PARAMS = {
    "noise_dim": 100,
    "epochs": 10,
    "batch_size": 256,
    "learning_rate": 0.0002,
    "nrows": None,  # Set to integer for testing
    "seed": 42,
    "device": device,
    "print_every": 100,
    "save_every": 10
}

# Model Configuration
MODEL_PARAMS = {
    "model_path": "multi_table_gan.pth",
    "preprocessor_dir": "preprocessor",
    "preprocessor_file": "preprocessor.pkl",
}

# Data Configuration
DATA_PARAMS = {
    "main_file": "main.csv",
    "performance_file": "performance.csv",
    "conversions_file": "conversions.csv",
    "synthetic_main_file": "synthetic_main.csv",
    "synthetic_performance_file": "synthetic_performance.csv",
    "synthetic_conversions_file": "synthetic_conversions.csv",
}

# Evaluation Configuration
EVALUATION_PARAMS = {
    "sample_size": 10000,  # Sample size for fast mode evaluation
    "random_state": 42,    # Random state for reproducible sampling
    "ks_test_threshold": 0.05,  # P-value threshold for KS test
    "acceptable_difference_threshold": 10.0,  # Acceptable difference percentage for integrity checks
    "currency_consistency_threshold": 5.0,    # Threshold for currency consistency scoring
    "temporal_consistency_threshold": 0.8,    # Threshold for temporal consistency
}

# Data Processing Configuration
PREPROCESSING_PARAMS = {
    "string_columns": [
        'client_id', 'campaign_id', 'campaign_name', 'campaign_currency',
        'mediarow_currency', 'mediaplan_id', 'mediaplan_name',
        'channel_id', 'channel_name', 'platform_id', 'mediarow_id',
        'mediarow_name', 'mediarow_goal_name', 'channel_mix_goal_name',
        'journey_goal_name', 'platform_name', 'conversion_name',
        'conversion_cost_based_name'
    ],
    "date_columns": {
        'main': ['mediarow_start_date', 'mediarow_end_date', 'mediaplan_start_date', 'mediaplan_end_date'],
        'performance': ['date'],
        'conversions': ['date']
    },
    "normalization_method": "z_score",  # or "min_max"
    "missing_value_strategy": "median",  # for numeric columns
    "categorical_missing_strategy": "empty_string",  # for categorical columns
}

PATHS = {
    # Core directories
    "OUTPUT_DIR": 'data/output',
    "MODELS_DIR": 'src/models',  # Pydantic models only
    "MODELS_LOCAL_DIR": 'models_local',  # Local model artifacts (not pushed to GitHub)
    "LOGS_DIR": 'src/logs',
    "DATA_DIR": 'src/data',
    # Output files for synthetic data
    "SYNTHETIC_MAIN_PATH": os.path.join('data/output', 'main_synthetic.csv'),
    "SYNTHETIC_PERFORMANCE_PATH": os.path.join('data/output', 'performance_synthetic.csv'),
    "SYNTHETIC_CONVERSIONS_PATH": os.path.join('data/output', 'conversions_synthetic.csv'),
    # Model artifacts (moved to local directory)
    "MODEL_PATH": os.path.join('models_local', MODEL_PARAMS['model_path']),
    "MAIN_MODEL_PATH": os.path.join('models_local', 'main_generator.pth'),
    "PERFORMANCE_MODEL_PATH": os.path.join('models_local', 'performance_generator.pth'),
    "CONVERSIONS_MODEL_PATH": os.path.join('models_local', 'conversions_generator.pth'),
    "PREPROCESSOR_DIR": os.path.join('models_local', MODEL_PARAMS['preprocessor_dir']),
    "PREPROCESSOR_PATH": os.path.join('models_local', MODEL_PARAMS['preprocessor_file']),
    # Other folders
    "PROCESSED_DIR": 'src/processed',
    "logs":'src/logs',
    "eval":'src/evaluation_plots',
    # Evaluation directories
    "EVAL_DIR": 'evaluation_results',
    "PLOTS_DIR": os.path.join('evaluation_results', 'plots'),
    # Database configuration paths
    "SQL_DIR": 'src/sql',
}



NAMING = {
        "prefixes" : [
        "Campaign", "Project", "Initiative", "Strategy", "Program", "Launch", "Drive",
        "Push", "Boost", "Focus", "Target", "Reach", "Connect", "Engage", "Activate",
        "Promote", "Brand", "Product", "Service", "Digital", "Mobile", "Online",
        "Social", "Display", "Search", "Video", "Native", "Performance", "Awareness"
    ],
    "regions" : [
        "US", "UK", "DE", "FR", "IT", "ES", "CA", "AU", "JP", "BR", "MX", "CH",
        "AT", "NL", "BE", "SE", "NO", "DK", "FI", "PL", "CZ", "HU", "RO", "GR",
        "Global", "EMEA", "APAC", "LATAM", "NAM", "EU", "DACH", "Nordics"
    ],
    "campaigns" : [
        "Awareness", "Consideration", "Conversion", "Retention", "Acquisition",
        "Performance", "Brand", "Product", "Launch", "Seasonal", "Holiday",
        "Q1", "Q2", "Q3", "Q4", "2024", "2025", "Spring", "Summer", "Fall", "Winter",
        "Always_On", "Retargeting", "Prospecting", "Lookalike", "Custom", "Premium"
    ],
    "channels" : [
        "Display", "Search", "Social", "Video", "Native", "Mobile", "Desktop",
        "Programmatic", "Direct", "Affiliate", "Email", "SMS", "Push", "OOH",
        "Radio", "TV", "Print", "Digital", "Offline", "Online", "Cross_Device"
    ]

}
