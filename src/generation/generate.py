import logging
import os
import pickle
from typing import Dict, Optional

import numpy as np
import pandas as pd
import torch

from src.config import settings
from src.gan.multi_table_gan import ConditionalGAN
from src.gan.networks.generator import Generator
from src.generation.relationship_enforcer import RelationshipEnforcer
from src.utils.utils import get_device

class DataGenerator:

    def __str__(self) -> str:
        return "DATA-GENERATOR"

    def __init__(self, num_samples: int = 1000, seed: int = 42, joint_training: bool = True,
                 real_data: Optional[Dict[str, pd.DataFrame]] = None):
        """
        Initialize the data generator with appropriate models based on training mode

        Args:
            num_samples: Number of samples to generate
            seed: Random seed for reproducibility
            joint_training: Whether to use joint training models
            real_data: Optional real data for pattern analysis
        """

        self.logger = logging.getLogger(str(self))
        self.num_samples = num_samples
        self.seed = seed
        self.device = get_device()
        self.preprocessor = self._load_preprocessor()
        self.joint_training = joint_training
        self.real_data = real_data

        # Initialize appropriate model based on training mode
        if joint_training:
            self.model = self._initialize_gan()
        else:
            self.model = self._initialize_single_generators()

    def _set_seed(self):
        """Set random seeds for reproducibility"""
        torch.manual_seed(self.seed)
        np.random.seed(self.seed)

    def _load_preprocessor(self):
        """Load the preprocessor from disk"""
        try:
            with open(settings.PATHS['PREPROCESSOR_PATH'], 'rb') as f:
                preprocessor = pickle.load(f)
            return preprocessor
        except FileNotFoundError:
            raise FileNotFoundError(f"Preprocessor not found at {settings.PATHS['PREPROCESSOR_PATH']}")

    def create_categorical_mask(self, columns, metadata):
        """Create boolean mask for categorical columns"""
        mask = []
        for col in columns:
            mask.append(metadata[col].get('type') == 'categorical')
        return torch.tensor(mask, dtype=torch.bool)

    def _initialize_gan(self) -> ConditionalGAN:
        """Initialize the ConditionalGAN model"""
        device = settings.TRAINING_PARAMS['device']
        noise_dim = settings.TRAINING_PARAMS['noise_dim']

        # Get dimensions from preprocessor metadata
        main_dim = len(self.preprocessor.column_metadata['main'])
        perf_dim = len(self.preprocessor.column_metadata['performance'])
        conv_dim = len(self.preprocessor.column_metadata['conversions'])

        # ConditionalGAN doesn't need categorical masks

        return ConditionalGAN(
            media_dim=main_dim,  # Media dimension table
            perf_dim=perf_dim,   # Performance table
            conv_dim=conv_dim,   # Conversions table
            noise_dim=noise_dim,
            device=str(device)
        )

    def _initialize_single_generators(self) -> Dict[str, Generator]:
        """Initialize single generators for each table"""
        generators = {}
        device = settings.TRAINING_PARAMS['device']
        noise_dim = settings.TRAINING_PARAMS['noise_dim']

        # Get dimensions and categorical columns for each table
        table_info = {
            'main': (
                len(self.preprocessor.column_metadata['main']),
                self.create_categorical_mask(
                    list(self.preprocessor.column_metadata['main'].keys()),
                    self.preprocessor.column_metadata['main']
                )
            ),
            'performance': (
                len(self.preprocessor.column_metadata['performance']),
                self.create_categorical_mask(
                    list(self.preprocessor.column_metadata['performance'].keys()),
                    self.preprocessor.column_metadata['performance']
                )
            ),
            'conversions': (
                len(self.preprocessor.column_metadata['conversions']),
                self.create_categorical_mask(
                    list(self.preprocessor.column_metadata['conversions'].keys()),
                    self.preprocessor.column_metadata['conversions']
                )
            )
        }

        for table, (dim, categorical_mask) in table_info.items():
            # Initialize generator
            generators[table] = Generator(
                noise_dim=noise_dim,
                output_dim=dim,
                categorical_mask=categorical_mask,
                device=device
            )

            # Load model weights with error handling
            try:
                model_path = settings.PATHS[f'{table.upper()}_MODEL_PATH']
                state_dict = torch.load(model_path, map_location=device)
                # Only load matching keys
                model_dict = generators[table].state_dict()
                state_dict = {k: v for k, v in state_dict.items() if k in model_dict}
                model_dict.update(state_dict)
                generators[table].load_state_dict(model_dict)
                generators[table].eval()
            except Exception as e:
                self.logger.warning(f"Warning: Could not load weights for {table} generator: {str(e)}")
                self.logger.warning("Using randomly initialized model instead.")

        return generators

    def generate(self) -> Optional[Dict[str, pd.DataFrame]]:
        """Generate synthetic data for all tables"""
        try:
            self._set_seed()

            if self.joint_training:
                # For conditional GAN, we need media data as input
                # For now, use dummy media data - this will be updated later
                media_data = torch.randn(self.num_samples, self.model.generator.media_dim, device=self.device)
                noise = torch.randn(self.num_samples, self.model.noise_dim, device=self.device)

                # Generate data using conditional GAN
                with torch.no_grad():
                    perf_data, conv_data = self.model.generate(media_data, self.num_samples)
                    main_data = media_data  # Media data becomes the main table

                # Move data to CPU and convert to DataFrame
                main_df = pd.DataFrame(
                    main_data.cpu().numpy(),
                    columns=self.preprocessor.column_metadata['main'].keys()
                )
                perf_df = pd.DataFrame(
                    perf_data.cpu().numpy(),
                    columns=self.preprocessor.column_metadata['performance'].keys()
                )
                conv_df = pd.DataFrame(
                    conv_data.cpu().numpy(),
                    columns=self.preprocessor.column_metadata['conversions'].keys()
                )
            else:
                # Generate noise vector on correct device
                noise = torch.randn(self.num_samples, self.model['main'].noise_dim, device=self.device)

                # Generate data for each table
                with torch.no_grad():
                    # Ensure all models and noise are on the correct device
                    main_data = self.model['main'](noise.to(self.device))
                    perf_data = self.model['performance'](noise.to(self.device))
                    conv_data = self.model['conversions'](noise.to(self.device))

                # Move data to CPU and convert to DataFrame
                main_df = pd.DataFrame(
                    main_data.cpu().numpy(),
                    columns=self.preprocessor.column_metadata['main'].keys()
                )
                perf_df = pd.DataFrame(
                    perf_data.cpu().numpy(),
                    columns=self.preprocessor.column_metadata['performance'].keys()
                )
                conv_df = pd.DataFrame(
                    conv_data.cpu().numpy(),
                    columns=self.preprocessor.column_metadata['conversions'].keys()
                )

            # Apply inverse preprocessing
            main_df = self.preprocessor.inverse_transform(main_df, 'main')
            perf_df = self.preprocessor.inverse_transform(perf_df, 'performance')
            conv_df = self.preprocessor.inverse_transform(conv_df, 'conversions')

            # Apply realistic missing patterns to synthetic data
            synthetic_data = {
                'main': main_df,
                'performance': perf_df,
                'conversions': conv_df
            }

            # Apply missing patterns if available
            synthetic_data_with_missing = self.preprocessor.apply_missing_patterns_to_synthetic(synthetic_data)
            main_df = synthetic_data_with_missing['main']
            perf_df = synthetic_data_with_missing['performance']
            conv_df = synthetic_data_with_missing['conversions']

            # Verify results are DataFrames
            for name, df in [('main', main_df), ('performance', perf_df), ('conversions', conv_df)]:
                if not isinstance(df, pd.DataFrame):
                    raise ValueError(f"Inverse transform for {name} returned {type(df)}, expected DataFrame")

            # Enforce relational constraints between tables
            self.logger.info("Enforcing relational constraints between tables...")
            enforcer = RelationshipEnforcer(seed=self.seed, real_data=self.real_data)
            main_df, perf_df, conv_df = enforcer.enforce_relationships(main_df, perf_df, conv_df)
            self.logger.info("Relational constraints enforced successfully!")

            # Convert all columns to string type before returning
            main_df = main_df.astype(str)
            perf_df = perf_df.astype(str)
            conv_df = conv_df.astype(str)

            return {
                'main': main_df,
                'performance': perf_df,
                'conversions': conv_df
            }
        except Exception as e:
            self.logger.error(f"Error generating data: {str(e)}")
            return None

    def save_synthetic_data(self, data: Dict[str, pd.DataFrame], output_dir: str = 'data/output') -> None:
        """Save generated synthetic data to CSV files"""
        os.makedirs(output_dir, exist_ok=True)

        for table_name, df in data.items():
            # Clean up missing values before saving
            df_clean = df.copy()

            # Replace various forms of missing values with empty strings
            df_clean = df_clean.fillna('')  # Replace NaN with empty string
            df_clean = df_clean.replace(['<NA>', 'nan', 'None', 'null'], '')  # Replace other missing indicators

            # Convert all columns to string type after cleaning missing values
            df_clean = df_clean.astype(str)

            # Replace any remaining "nan" strings that might have been created
            df_clean = df_clean.replace('nan', '')

            df_clean.to_csv(os.path.join(output_dir, f"{table_name}_synthetic.csv"), index=False, quoting=1)
            self.logger.info(f"Saved {table_name} data to {output_dir}/{table_name}_synthetic.csv (missing values as empty strings)")


