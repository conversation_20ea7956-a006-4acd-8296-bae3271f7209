import pandas as pd
import numpy as np
from typing import Dict, Any, <PERSON>, Tuple, Optional
import logging
from collections import defaultdict


class PatternAnalyzer:
    """
    Analyzes real data patterns and provides methods to replicate those patterns
    in synthetic data generation. This ensures synthetic data matches real-world
    inconsistencies and patterns rather than being artificially perfect.
    """

    def __init__(self, seed: int = 42):
        """Initialize the pattern analyzer with a random seed for reproducibility"""
        self.seed = seed
        np.random.seed(seed)
        self.logger = logging.getLogger("PATTERN-ANALYZER")
        self.patterns = {}

    def analyze_real_data_patterns(self, real_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Analyze patterns in real data that should be replicated in synthetic data.

        Args:
            real_data: Dictionary containing real data tables

        Returns:
            Dictionary containing analyzed patterns
        """
        self.logger.info("Analyzing real data patterns for synthetic replication")

        patterns = {}

        # Analyze currency consistency patterns
        if 'main' in real_data:
            patterns['currency_consistency'] = self._analyze_currency_consistency(real_data['main'])

        # Analyze null patterns
        for table_name, df in real_data.items():
            patterns[f'{table_name}_null_patterns'] = self._analyze_null_patterns(df, table_name)

        # Analyze categorical distribution patterns
        for table_name, df in real_data.items():
            patterns[f'{table_name}_categorical_patterns'] = self._analyze_categorical_patterns(df, table_name)

        # Store patterns for later use
        self.patterns = patterns

        self.logger.info(f"Analyzed {len(patterns)} pattern types from real data")
        return patterns

    def _analyze_currency_consistency(self, main_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze currency consistency patterns in real data"""
        if 'campaign_currency' not in main_df.columns or 'mediarow_currency' not in main_df.columns:
            return {'consistency_rate': 0.74, 'available': False}

        # Calculate actual consistency rate
        valid_rows = main_df.dropna(subset=['campaign_currency', 'mediarow_currency'])
        if len(valid_rows) == 0:
            return {'consistency_rate': 0.74, 'available': False}

        matching = (valid_rows['campaign_currency'] == valid_rows['mediarow_currency']).sum()
        consistency_rate = matching / len(valid_rows)

        # Analyze currency distributions
        campaign_currencies = main_df['campaign_currency'].value_counts(normalize=True).to_dict()
        mediarow_currencies = main_df['mediarow_currency'].value_counts(normalize=True).to_dict()

        self.logger.info(f"Real data currency consistency: {consistency_rate:.1%}")

        return {
            'consistency_rate': consistency_rate,
            'available': True,
            'campaign_currency_distribution': campaign_currencies,
            'mediarow_currency_distribution': mediarow_currencies,
            'total_valid_rows': len(valid_rows)
        }

    def _analyze_null_patterns(self, df: pd.DataFrame, table_name: str) -> Dict[str, Any]:
        """Analyze null value patterns in real data"""
        null_patterns = {}

        for col in df.columns:
            null_rate = df[col].isnull().sum() / len(df)
            null_patterns[col] = {
                'null_rate': null_rate,
                'total_rows': len(df),
                'null_count': df[col].isnull().sum()
            }

        # Analyze correlated null patterns (columns that tend to be null together)
        correlated_nulls = {}
        for col1 in df.columns:
            for col2 in df.columns:
                if col1 != col2:
                    # Calculate correlation between null patterns
                    null_corr = df[col1].isnull().corr(df[col2].isnull())
                    if abs(null_corr) > 0.3:  # Significant correlation
                        correlated_nulls[f'{col1}_vs_{col2}'] = null_corr

        return {
            'individual_null_rates': null_patterns,
            'correlated_null_patterns': correlated_nulls
        }

    def _analyze_categorical_patterns(self, df: pd.DataFrame, table_name: str) -> Dict[str, Any]:
        """Analyze categorical value distribution patterns"""
        categorical_patterns = {}

        categorical_cols = df.select_dtypes(include=['object', 'category']).columns

        for col in categorical_cols:
            if 'date' not in col.lower():  # Skip date columns
                value_counts = df[col].value_counts(normalize=True)

                categorical_patterns[col] = {
                    'distribution': value_counts.to_dict(),
                    'unique_count': df[col].nunique(),
                    'top_values': value_counts.head(10).to_dict(),
                    'entropy': -np.sum(value_counts * np.log2(value_counts + 1e-10))
                }

        return categorical_patterns

    def apply_currency_consistency_pattern(self, df: pd.DataFrame,
                                         campaign_col: str = 'campaign_currency',
                                         mediarow_col: str = 'mediarow_currency') -> pd.DataFrame:
        """
        Apply real data currency consistency patterns to synthetic data

        Args:
            df: DataFrame to modify
            campaign_col: Name of campaign currency column
            mediarow_col: Name of mediarow currency column

        Returns:
            Modified DataFrame with realistic currency consistency
        """
        if 'currency_consistency' not in self.patterns:
            self.logger.warning("No currency consistency pattern available, using default 74.37%")
            target_consistency = 0.7437
        else:
            pattern = self.patterns['currency_consistency']
            if pattern['available']:
                target_consistency = pattern['consistency_rate']
                self.logger.info(f"Using real data consistency rate: {target_consistency:.1%}")
            else:
                target_consistency = 0.7437

        return self._apply_consistency_pattern(df, campaign_col, mediarow_col, target_consistency)

    def _apply_consistency_pattern(self, df: pd.DataFrame, col1: str, col2: str,
                                 consistency_rate: float) -> pd.DataFrame:
        """Apply a specific consistency pattern between two columns"""
        result_df = df.copy()

        if col1 not in df.columns or col2 not in df.columns:
            return result_df

        # Get unique values from both columns
        all_values = set()
        if df[col1].notna().any():
            all_values.update(df[col1].dropna().unique())
        if df[col2].notna().any():
            all_values.update(df[col2].dropna().unique())

        if not all_values:
            return result_df

        all_values = list(all_values)

        # Apply consistency pattern
        for idx, row in result_df.iterrows():
            if pd.isna(row[col1]) and pd.isna(row[col2]):
                continue

            # Handle null cases
            if pd.isna(row[col1]) and not pd.isna(row[col2]):
                result_df.loc[idx, col1] = row[col2]
            elif pd.isna(row[col2]) and not pd.isna(row[col1]):
                result_df.loc[idx, col2] = row[col1]
            else:
                # Both values exist - apply consistency pattern
                if np.random.random() < consistency_rate:
                    # Make them match
                    result_df.loc[idx, col2] = row[col1]
                else:
                    # Make them different
                    different_values = [v for v in all_values if v != row[col1]]
                    if different_values:
                        result_df.loc[idx, col2] = np.random.choice(different_values)

        return result_df

    def apply_null_patterns(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """Apply real data null patterns to synthetic data"""
        pattern_key = f'{table_name}_null_patterns'
        if pattern_key not in self.patterns:
            self.logger.warning(f"No null patterns available for {table_name}")
            return df

        null_patterns = self.patterns[pattern_key]['individual_null_rates']
        result_df = df.copy()

        # Apply individual null rates
        for col, pattern in null_patterns.items():
            if col in result_df.columns:
                null_rate = pattern['null_rate']
                # Randomly set values to null based on real data rate
                mask = np.random.random(len(result_df)) < null_rate
                result_df.loc[mask, col] = np.nan

        return result_df

    def apply_generic_consistency_pattern(self, df: pd.DataFrame, col1: str, col2: str,
                                        target_consistency: float) -> pd.DataFrame:
        """
        Apply a generic consistency pattern between any two columns.
        This can be used for other consistency patterns beyond currency.

        Args:
            df: DataFrame to modify
            col1: First column name
            col2: Second column name
            target_consistency: Target consistency rate (0.0 to 1.0)

        Returns:
            Modified DataFrame with applied consistency pattern
        """
        self.logger.info(f"Applying {target_consistency:.1%} consistency pattern between {col1} and {col2}")
        return self._apply_consistency_pattern(df, col1, col2, target_consistency)

    def get_pattern_summary(self) -> Dict[str, Any]:
        """Get a summary of all analyzed patterns"""
        summary = {}

        if 'currency_consistency' in self.patterns:
            pattern = self.patterns['currency_consistency']
            if pattern['available']:
                summary['currency_consistency_rate'] = f"{pattern['consistency_rate']:.1%}"
            else:
                summary['currency_consistency_rate'] = "Not available"

        # Count null pattern tables
        null_pattern_tables = [k for k in self.patterns.keys() if 'null_patterns' in k]
        summary['null_pattern_tables'] = len(null_pattern_tables)

        # Count categorical pattern tables
        categorical_pattern_tables = [k for k in self.patterns.keys() if 'categorical_patterns' in k]
        summary['categorical_pattern_tables'] = len(categorical_pattern_tables)

        return summary
