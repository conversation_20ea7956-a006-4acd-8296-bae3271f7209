import pandas as pd
import numpy as np
from typing import Tuple, Dict, Any, List, Optional
import logging
from datetime import datetime, timedelta
import random
from collections import defaultdict
from .pattern_analyzer import PatternAnalyzer



class RelationshipEnforcer:
    """
    Enforces relational constraints between synthetic data tables to ensure
    realistic relationships between main, performance, and conversions tables.
    """

    def __str__(self) -> str:
        return "RELATIONSHIP-ENFORCER"

    def __init__(self, seed: int = 42, real_data: Optional[Dict[str, pd.DataFrame]] = None):
        """Initialize the relationship enforcer with a random seed for reproducibility"""
        self.seed = seed
        random.seed(seed)
        np.random.seed(seed)
        self.logger = logging.getLogger(str(self))

        # Initialize pattern analyzer
        self.pattern_analyzer = PatternAnalyzer(seed=seed)

        # Analyze real data patterns if available
        if real_data is not None:
            self.logger.info("Analyzing real data patterns for synthetic replication")
            self.pattern_analyzer.analyze_real_data_patterns(real_data)
        else:
            self.logger.info("No real data provided - using default patterns")

    def enforce_relationships(self,
                            main_df: pd.DataFrame,
                            performance_df: pd.DataFrame,
                            conversions_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Enforce all relational constraints between the three tables.

        Args:
            main_df: Main table with mediarow information
            performance_df: Performance metrics table
            conversions_df: Conversions table

        Returns:
            Tuple of corrected DataFrames (main, performance, conversions)
        """
        self.logger.info("Starting relationship enforcement between synthetic tables")

        # Step 1: CRITICAL - Fix hierarchical tree structure in main table
        main_df = self._enforce_hierarchical_integrity(main_df)

        # Step 2: Fix date constraints in main table
        main_df = self._fix_main_table_dates(main_df)

        # Step 3: Ensure foreign key relationships
        performance_df = self._enforce_foreign_keys(main_df, performance_df, 'performance')
        conversions_df = self._enforce_foreign_keys(main_df, conversions_df, 'conversions')

        # Step 4: Fix date ranges in performance and conversions
        performance_df = self._fix_date_ranges(main_df, performance_df, 'performance')
        conversions_df = self._fix_date_ranges(main_df, conversions_df, 'conversions')

        # Step 5: Enforce budget constraints
        performance_df = self._enforce_budget_constraints(main_df, performance_df)

        # Step 6: Ensure currency consistency (including within main table)
        main_df = self._enforce_main_table_currency_consistency(main_df)
        performance_df = self._enforce_currency_consistency(main_df, performance_df, 'performance')
        conversions_df = self._enforce_currency_consistency(main_df, conversions_df, 'conversions')

        self.logger.info("Relationship enforcement completed successfully")
        return main_df, performance_df, conversions_df

    def _enforce_hierarchical_integrity(self, main_df: pd.DataFrame) -> pd.DataFrame:
        """
        Enforce proper hierarchical tree structure:
        Client (1) -> Campaign (multiple) -> Mediaplan/Channel/Platform (multiple) -> Mediarow (multiple)

        Ensures:
        - Each campaign_id belongs to exactly one client_id
        - Each mediaplan_id belongs to exactly one campaign_id
        - Each channel_id belongs to exactly one campaign_id
        - Each platform_id belongs to exactly one campaign_id
        - Realistic distribution patterns matching real data
        """
        self.logger.info("Enforcing hierarchical tree structure integrity")

        df = main_df.copy()

        # Get all ID columns that need hierarchical enforcement
        id_columns = ['client_id', 'campaign_id', 'mediaplan_id', 'channel_id', 'platform_id', 'mediarow_id']

        # Step 1: Build hierarchical structure from top down
        self.logger.info("Building hierarchical structure from clients down to mediarows")

        # Get unique clients and create realistic campaign distributions
        unique_clients = df['client_id'].unique()

        # Create hierarchical mapping
        hierarchy_map = defaultdict(lambda: {'campaigns': [], 'campaign_details': defaultdict(dict)})

        # For each client, assign campaigns with realistic distribution
        for client_id in unique_clients:
            # Get realistic number of campaigns per client (based on real data patterns)
            # Real data: Mean=23.9, Max=569, so we'll use a more aggressive distribution
            num_campaigns = max(1, int(np.random.exponential(20) + 5))  # Higher baseline
            num_campaigns = min(num_campaigns, 200)  # Higher cap for realism

            # Generate unique campaign IDs for this client
            client_campaigns = []
            for _ in range(num_campaigns):
                # Create unique campaign ID
                campaign_id = int(np.random.randint(100, 9999))
                while campaign_id in client_campaigns:
                    campaign_id = int(np.random.randint(100, 9999))
                client_campaigns.append(campaign_id)

            hierarchy_map[client_id]['campaigns'] = client_campaigns

            # For each campaign, create mediaplans, channels, platforms
            for campaign_id in client_campaigns:
                # Realistic number of mediaplans per campaign (Real: Mean=2.1, Max=31)
                num_mediaplans = max(1, int(np.random.exponential(1.8) + 1))
                num_mediaplans = min(num_mediaplans, 25)

                # Realistic number of channels per campaign (Real: Mean=5.5, Max=151)
                num_channels = max(1, int(np.random.exponential(4.5) + 1))
                num_channels = min(num_channels, 50)

                # Realistic number of platforms per campaign
                num_platforms = max(1, int(np.random.exponential(3) + 1))
                num_platforms = min(num_platforms, 30)

                # Generate unique IDs
                campaign_mediaplans = [int(np.random.randint(10000, 999999)) for _ in range(num_mediaplans)]
                campaign_channels = [int(np.random.randint(10000, 999999)) for _ in range(num_channels)]
                campaign_platforms = [int(np.random.randint(10000, 999999)) for _ in range(num_platforms)]

                hierarchy_map[client_id]['campaign_details'][campaign_id] = {
                    'mediaplans': campaign_mediaplans,
                    'channels': campaign_channels,
                    'platforms': campaign_platforms
                }

        # Step 2: Assign hierarchical IDs to each row
        self.logger.info("Assigning hierarchical IDs to maintain referential integrity")

        for idx, row in df.iterrows():
            client_id = row['client_id']

            # Ensure this client exists in our hierarchy
            if client_id not in hierarchy_map:
                # Assign to a random existing client
                client_id = np.random.choice(list(hierarchy_map.keys()))
                df.loc[idx, 'client_id'] = client_id

            # Assign campaign from this client's campaigns
            available_campaigns = hierarchy_map[client_id]['campaigns']
            campaign_id = np.random.choice(available_campaigns)
            df.loc[idx, 'campaign_id'] = campaign_id

            # Get campaign details
            campaign_details = hierarchy_map[client_id]['campaign_details'][campaign_id]

            # Assign mediaplan, channel, platform from this campaign
            df.loc[idx, 'mediaplan_id'] = np.random.choice(campaign_details['mediaplans'])
            df.loc[idx, 'channel_id'] = np.random.choice(campaign_details['channels'])
            df.loc[idx, 'platform_id'] = np.random.choice(campaign_details['platforms'])

            # Mediarow ID can be unique (it's the leaf node)
            df.loc[idx, 'mediarow_id'] = int(np.random.randint(10000, 999999))

        # Step 3: Verify and fix any remaining violations
        violations = self._check_hierarchical_violations(df)
        if violations['total'] > 0:
            self.logger.info(f"Fixing {violations['total']} remaining hierarchical violations...")
            df = self._fix_remaining_violations(df)

            # Verify again
            violations = self._check_hierarchical_violations(df)
            if violations['total'] == 0:
                self.logger.info("✅ All hierarchical violations fixed successfully")
            else:
                self.logger.warning(f"⚠️ {violations['total']} hierarchical violations still exist after final fix attempt")
        else:
            self.logger.info("✅ Hierarchical integrity successfully enforced - no violations found")

        return df

    def _check_hierarchical_violations(self, df: pd.DataFrame) -> Dict[str, int]:
        """Check for hierarchical integrity violations"""
        violations = {}

        # Check campaigns with multiple clients
        campaign_clients = df.groupby('campaign_id')['client_id'].nunique()
        violations['campaigns_multiple_clients'] = (campaign_clients > 1).sum()

        # Check mediaplans with multiple campaigns
        mediaplan_campaigns = df.groupby('mediaplan_id')['campaign_id'].nunique()
        violations['mediaplans_multiple_campaigns'] = (mediaplan_campaigns > 1).sum()

        # Check channels with multiple campaigns
        channel_campaigns = df.groupby('channel_id')['campaign_id'].nunique()
        violations['channels_multiple_campaigns'] = (channel_campaigns > 1).sum()

        # Check platforms with multiple campaigns
        platform_campaigns = df.groupby('platform_id')['campaign_id'].nunique()
        violations['platforms_multiple_campaigns'] = (platform_campaigns > 1).sum()

        violations['total'] = sum(violations.values())
        return violations

    def _fix_remaining_violations(self, df: pd.DataFrame) -> pd.DataFrame:
        """Fix any remaining hierarchical violations by reassigning conflicting IDs"""
        result_df = df.copy()

        # Fix campaigns with multiple clients
        campaign_clients = result_df.groupby('campaign_id')['client_id'].nunique()
        problematic_campaigns = campaign_clients[campaign_clients > 1].index

        for campaign_id in problematic_campaigns:
            # Get all rows with this campaign_id
            campaign_mask = result_df['campaign_id'] == campaign_id
            campaign_rows = result_df[campaign_mask]

            # Keep the first client_id, reassign campaign_id for others
            first_client = campaign_rows.iloc[0]['client_id']
            for idx in campaign_rows.index[1:]:
                if result_df.loc[idx, 'client_id'] != first_client:
                    # Generate new unique campaign_id
                    new_campaign_id = int(np.random.randint(100, 9999))
                    while new_campaign_id in result_df['campaign_id'].values:
                        new_campaign_id = int(np.random.randint(100, 9999))
                    result_df.loc[idx, 'campaign_id'] = new_campaign_id

        # Fix mediaplans with multiple campaigns
        mediaplan_campaigns = result_df.groupby('mediaplan_id')['campaign_id'].nunique()
        problematic_mediaplans = mediaplan_campaigns[mediaplan_campaigns > 1].index

        for mediaplan_id in problematic_mediaplans:
            mediaplan_mask = result_df['mediaplan_id'] == mediaplan_id
            mediaplan_rows = result_df[mediaplan_mask]

            first_campaign = mediaplan_rows.iloc[0]['campaign_id']
            for idx in mediaplan_rows.index[1:]:
                if result_df.loc[idx, 'campaign_id'] != first_campaign:
                    # Generate new unique mediaplan_id
                    new_mediaplan_id = int(np.random.randint(10000, 999999))
                    while new_mediaplan_id in result_df['mediaplan_id'].values:
                        new_mediaplan_id = int(np.random.randint(10000, 999999))
                    result_df.loc[idx, 'mediaplan_id'] = new_mediaplan_id

        # Fix channels with multiple campaigns
        channel_campaigns = result_df.groupby('channel_id')['campaign_id'].nunique()
        problematic_channels = channel_campaigns[channel_campaigns > 1].index

        for channel_id in problematic_channels:
            channel_mask = result_df['channel_id'] == channel_id
            channel_rows = result_df[channel_mask]

            first_campaign = channel_rows.iloc[0]['campaign_id']
            for idx in channel_rows.index[1:]:
                if result_df.loc[idx, 'campaign_id'] != first_campaign:
                    # Generate new unique channel_id
                    new_channel_id = int(np.random.randint(10000, 999999))
                    while new_channel_id in result_df['channel_id'].values:
                        new_channel_id = int(np.random.randint(10000, 999999))
                    result_df.loc[idx, 'channel_id'] = new_channel_id

        # Fix platforms with multiple campaigns
        platform_campaigns = result_df.groupby('platform_id')['campaign_id'].nunique()
        problematic_platforms = platform_campaigns[platform_campaigns > 1].index

        for platform_id in problematic_platforms:
            platform_mask = result_df['platform_id'] == platform_id
            platform_rows = result_df[platform_mask]

            first_campaign = platform_rows.iloc[0]['campaign_id']
            for idx in platform_rows.index[1:]:
                if result_df.loc[idx, 'campaign_id'] != first_campaign:
                    # Generate new unique platform_id
                    new_platform_id = int(np.random.randint(10000, 999999))
                    while new_platform_id in result_df['platform_id'].values:
                        new_platform_id = int(np.random.randint(10000, 999999))
                    result_df.loc[idx, 'platform_id'] = new_platform_id

        return result_df

    def _enforce_main_table_currency_consistency(self, main_df: pd.DataFrame) -> pd.DataFrame:
        """
        Enforce currency consistency within the main table using real data patterns.

        Uses PatternAnalyzer to replicate actual currency consistency patterns from real data
        instead of artificially perfect consistency.
        """
        self.logger.info("Enforcing currency consistency within main table using real data patterns")

        # Use pattern analyzer to apply realistic currency consistency
        df = self.pattern_analyzer.apply_currency_consistency_pattern(
            main_df.copy(),
            campaign_col='campaign_currency',
            mediarow_col='mediarow_currency'
        )

        # Verify the consistency
        if 'campaign_currency' in df.columns and 'mediarow_currency' in df.columns:
            valid_rows = df.dropna(subset=['campaign_currency', 'mediarow_currency'])
            if len(valid_rows) > 0:
                matching = (valid_rows['campaign_currency'] == valid_rows['mediarow_currency']).sum()
                consistency_pct = (matching / len(valid_rows)) * 100
                self.logger.info(f"Currency consistency achieved: {consistency_pct:.1f}% (matches real data pattern)")

        return df

    def _fix_main_table_dates(self, main_df: pd.DataFrame) -> pd.DataFrame:
        """Ensure start dates come before end dates in main table"""
        self.logger.info("Fixing date constraints in main table")

        df = main_df.copy()

        # Convert date columns to datetime
        date_cols = ['mediarow_start_date', 'mediarow_end_date', 'mediaplan_start_date', 'mediaplan_end_date']
        for col in date_cols:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')

        # Fix mediarow dates
        if 'mediarow_start_date' in df.columns and 'mediarow_end_date' in df.columns:
            # Where end date is before start date, swap them
            mask = df['mediarow_end_date'] < df['mediarow_start_date']
            if mask.sum() > 0:
                self.logger.info(f"Fixing {mask.sum()} mediarow date inversions")
                df.loc[mask, ['mediarow_start_date', 'mediarow_end_date']] = \
                    df.loc[mask, ['mediarow_end_date', 'mediarow_start_date']].values

        # Fix mediaplan dates
        if 'mediaplan_start_date' in df.columns and 'mediaplan_end_date' in df.columns:
            # Where end date is before start date, swap them
            mask = df['mediaplan_end_date'] < df['mediaplan_start_date']
            if mask.sum() > 0:
                self.logger.info(f"Fixing {mask.sum()} mediaplan date inversions")
                df.loc[mask, ['mediaplan_start_date', 'mediaplan_end_date']] = \
                    df.loc[mask, ['mediaplan_end_date', 'mediaplan_start_date']].values

        return df

    def _enforce_foreign_keys(self, main_df: pd.DataFrame, child_df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """Ensure child table only contains keys that exist in main table"""
        self.logger.info(f"Enforcing foreign key constraints for {table_name} table")

        # Get valid keys from main table
        key_columns = ['client_id', 'campaign_id', 'mediarow_id']
        valid_keys = main_df[key_columns].drop_duplicates()

        # Sample child table rows to match valid keys
        if len(child_df) > len(valid_keys):
            # If we have more child rows than valid keys, sample with replacement
            sampled_keys = valid_keys.sample(n=len(child_df), replace=True, random_state=self.seed)
        else:
            # If we have fewer child rows, sample without replacement
            sampled_keys = valid_keys.sample(n=len(child_df), replace=False, random_state=self.seed)

        # Reset index to align with child_df
        sampled_keys = sampled_keys.reset_index(drop=True)

        # Create new child dataframe with valid keys
        result_df = child_df.copy()
        for col in key_columns:
            if col in result_df.columns:
                result_df[col] = sampled_keys[col].values

        self.logger.info(f"Updated {table_name} table with {len(result_df)} rows using valid foreign keys")
        return result_df

    def _fix_date_ranges(self, main_df: pd.DataFrame, child_df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """Ensure dates in child tables fall within valid ranges from main table"""
        self.logger.info(f"Fixing date ranges for {table_name} table")

        if 'date' not in child_df.columns:
            return child_df

        df = child_df.copy()
        df['date'] = pd.to_datetime(df['date'], errors='coerce')

        # Create lookup for date ranges
        main_lookup = main_df.set_index(['client_id', 'campaign_id', 'mediarow_id'])

        fixed_dates = []
        for idx, row in df.iterrows():
            try:
                # Get corresponding main table row
                key = (row['client_id'], row['campaign_id'], row['mediarow_id'])
                main_row = main_lookup.loc[key]

                # Get valid date range (prefer mediarow dates, fallback to mediaplan dates)
                start_date = main_row.get('mediarow_start_date') or main_row.get('mediaplan_start_date')
                end_date = main_row.get('mediarow_end_date') or main_row.get('mediaplan_end_date')

                if pd.isna(start_date) or pd.isna(end_date):
                    # If no valid dates, use current date
                    fixed_dates.append(pd.Timestamp.now().date())
                else:
                    # Generate random date within valid range
                    start_ts = pd.Timestamp(start_date)
                    end_ts = pd.Timestamp(end_date)

                    if start_ts <= end_ts:
                        # Generate random date between start and end
                        days_diff = (end_ts - start_ts).days
                        if days_diff > 0:
                            random_days = random.randint(0, days_diff)
                            random_date = start_ts + timedelta(days=random_days)
                        else:
                            random_date = start_ts
                        fixed_dates.append(random_date.date())
                    else:
                        # If dates are still inverted, use start date
                        fixed_dates.append(start_ts.date())

            except (KeyError, IndexError):
                # If key not found, use current date
                fixed_dates.append(pd.Timestamp.now().date())

        df['date'] = fixed_dates
        self.logger.info(f"Fixed {len(fixed_dates)} dates in {table_name} table")
        return df

    def _enforce_budget_constraints(self, main_df: pd.DataFrame, performance_df: pd.DataFrame) -> pd.DataFrame:
        """Ensure total spend per mediarow doesn't exceed budget"""
        self.logger.info("Enforcing budget constraints")

        df = performance_df.copy()

        # Group by mediarow keys and calculate total spend
        spend_cols = ['spend_in_mediarow_currency', 'spend_in_campaign_currency']
        key_cols = ['client_id', 'campaign_id', 'mediarow_id']

        # Create lookup for budgets
        budget_lookup = main_df.set_index(key_cols)

        for spend_col in spend_cols:
            if spend_col not in df.columns:
                continue

            budget_col = f"mediarow_lifetime_budget_in_{spend_col.split('_in_')[1]}"
            if budget_col not in main_df.columns:
                continue

            # Calculate current spend by mediarow
            current_spend = df.groupby(key_cols)[spend_col].sum()

            # Check which mediarows exceed budget
            for key, total_spend in current_spend.items():
                try:
                    budget = budget_lookup.loc[key, budget_col]
                    if pd.notna(budget) and total_spend > budget:
                        # Scale down all spend values for this mediarow
                        scale_factor = budget / total_spend * 0.9  # Use 90% of budget for safety
                        mask = (df['client_id'] == key[0]) & \
                               (df['campaign_id'] == key[1]) & \
                               (df['mediarow_id'] == key[2])
                        df.loc[mask, spend_col] *= scale_factor

                except (KeyError, IndexError):
                    continue

        self.logger.info("Budget constraints enforced")
        return df

    def _enforce_currency_consistency(self, main_df: pd.DataFrame, child_df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """Ensure currency columns are consistent between tables"""
        self.logger.info(f"Enforcing currency consistency for {table_name} table")

        df = child_df.copy()

        # Currency columns to check
        currency_mappings = {
            'mediarow_currency': 'mediarow_currency',
            'campaign_currency': 'campaign_currency'
        }

        # Create lookup for currencies
        key_cols = ['client_id', 'campaign_id', 'mediarow_id']
        currency_lookup = main_df.set_index(key_cols)

        for child_col, main_col in currency_mappings.items():
            if child_col in df.columns and main_col in main_df.columns:
                # Update child table currencies to match main table
                for idx, row in df.iterrows():
                    try:
                        key = (row['client_id'], row['campaign_id'], row['mediarow_id'])
                        correct_currency = currency_lookup.loc[key, main_col]
                        if pd.notna(correct_currency):
                            df.loc[idx, child_col] = correct_currency
                    except (KeyError, IndexError):
                        continue

        self.logger.info(f"Currency consistency enforced for {table_name} table")
        return df
