-- Filter out mediarows from test and M2 clients

SELECT *
FROM ds_{table_name}_main
WHERE CAST(mediarow_id as INT) IN (
    SELECT distinct CAST(mediarow_id as INT)
    FROM ds_media_dim_main AS dim
    JOIN ds_mediaplan_client_main AS client
        ON CAST(dim.client_id AS int) = CAST(client.client_id AS int)
    WHERE LOWER(client.client_name) NOT LIKE '%test%' AND LOWER(client.client_name) NOT LIKE '%m2%'
)