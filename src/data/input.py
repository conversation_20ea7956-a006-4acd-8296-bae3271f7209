import concurrent.futures
import logging
import time
from typing import Any, Dict, <PERSON>ple

import pandas as pd

from src.app.components.payload import Payload
from src.clients.athena_client import AthenaClient


class InputData:

    def __str__(self) -> str:
        return "INPUT-DATA"

    def __init__(self, payload: Payload):
        """Initializes the InputData class."""
        self.db = AthenaClient()  # Use default initialization
        self.payload = payload
        self.logger = logging.getLogger(str(self))

    def _get_training_table(self, params: Dict[str,Any]) -> pd.DataFrame:
        """Loads the media dimension dataset from the database.

        Args:
            params (Dict[str, Any]): Parameters to format the query.

        Returns:
            pd.DataFrame: The result of the SQL query.
        """
        return self.db.run_query(
            q="src/sql/training.sql",
            read=True,
            params=params
        )

    def _get_inference_table(self, params: Dict[str,Any]) -> pd.DataFrame:
        """Loads the media dimension dataset from the database.

        Args:
            params (Dict[str, Any]): Parameters to format the query.

        Returns:
            pd.DataFrame: The result of the SQL query
        """
        return self.db.run_query(
            q="src/sql/inference.sql",
            read=True,
            params=params
        )

    def training_set(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Loads the training datasets from the database.

        Returns:
            Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]: Tuple of media dimension, performance, and performance conversions datasets.
        """
        self.logger.info("Loading training data from database...")

        # Table 1: Main (media_dim)
        self.logger.info("Loading main table (media_dim)...")
        start_time = time.time()
        main_df = self._get_training_table({"table_name": "media_dim"})
        elapsed = time.time() - start_time
        self.logger.info(f"Main table loaded: {main_df.shape[0]:,} rows in {elapsed:.1f}s")

        # Table 2: Performance
        self.logger.info("Loading performance table...")
        start_time = time.time()
        performance_df = self._get_training_table({"table_name": "performance"})
        elapsed = time.time() - start_time
        self.logger.info(f"Performance table loaded: {performance_df.shape[0]:,} rows in {elapsed:.1f}s")

        # Table 3: Conversions
        self.logger.info("Loading conversions table...")
        start_time = time.time()
        conversions_df = self._get_training_table({"table_name": "performance_conversions"})
        elapsed = time.time() - start_time
        self.logger.info(f"Conversions table loaded: {conversions_df.shape[0]:,} rows in {elapsed:.1f}s")

        total_rows = main_df.shape[0] + performance_df.shape[0] + conversions_df.shape[0]
        self.logger.info(f"All training data loaded successfully. Total rows: {total_rows:,}")

        return main_df, performance_df, conversions_df

    def training_set_parallel(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Loads the training datasets from the database using parallel execution.

        Returns:
            Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]: Tuple of media dimension, performance, and performance conversions datasets.
        """
        with concurrent.futures.ThreadPoolExecutor() as executor:
            media_dim_future = executor.submit(self._get_training_table, {"table_name": 'media_dim'})
            performance_future = executor.submit(self._get_training_table, {"table_name": 'performance'})
            performance_conversions_future = executor.submit(self._get_training_table, {"table_name": 'performance_conversions'})

            media_dim_df = media_dim_future.result()
            performance_df = performance_future.result()
            performance_conversions_df = performance_conversions_future.result()

            return {
                "data":{
                    "media":media_dim_df, 
                    "performance":performance_df, 
                    "performance_conversions":performance_conversions_df
                }
            } 

    def inference_set(self) -> pd.DataFrame:
        """Loads inference dataset for specific client/campaign/mediaplan from the database.

        Args:
            client_id (str): Client ID to filter data
            campaign_id (str): Campaign ID to filter data
            mediaplan_id (str): Mediaplan ID to filter data

        Returns:
            pd.DataFrame: Filtered media dimension dataset for inference
        """
        params = {
            "client_id": self.payload.client_id,
            "campaign_id": self.payload.campaign_id,
            "mediaplan_id": self.payload.mediaplan_id
        }

        return self._get_inference_table(params)
