import pandas as pd
import numpy as np
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

from .cleaners import DataCleaner
from .anonymizers import AnonymizerFactory


class DataTransformer:
    """Class responsible for data transformation and processing"""

    def __str__(self) -> str:
        return "DATA-TRANSFORMER"

    def __init__(self):
        self.logger = logging.getLogger(str(self))
        self.cleaner = DataCleaner()

    def anonymize_campaign_names(self, categories: List[str]) -> List[str]:
        """
        Anonymize campaign names while maintaining realistic structure and diversity.

        Args:
            categories: List of original campaign names

        Returns:
            List of anonymized campaign names with same length and diversity
        """
        anonymizer = AnonymizerFactory.get_anonymizer('campaign_name')
        return anonymizer.anonymize(categories)

    def anonymize_media_plan_names(self, categories: List[str]) -> List[str]:
        """
        Anonymize media plan names while preserving month/year information and anonymizing company names.

        Args:
            categories: List of original media plan names

        Returns:
            List of anonymized media plan names preserving temporal information
        """
        anonymizer = AnonymizerFactory.get_anonymizer('mediaplan_name')
        return anonymizer.anonymize(categories)

    def process_datetime(self, col: pd.Series) -> pd.DataFrame:
        """Convert datetime column to numeric timestamp using date-only format"""
        try:
            # Convert to datetime if not already
            dt = pd.to_datetime(col)
            # Convert to date-only format
            date_only = dt.dt.date
            # Convert to numeric timestamp (seconds since epoch)
            timestamp = pd.to_datetime(date_only).astype('int64') // 10**9
            return pd.DataFrame({col.name: timestamp})
        except Exception as e:
            self.logger.error(f"Error processing datetime column {col.name}: {str(e)}")
            # Return zeros if conversion fails
            return pd.DataFrame({col.name: np.zeros(len(col))})

    def process_categorical(self, col: pd.Series, table_name: str, column_metadata: Dict[str, Dict[str, Any]]) -> pd.DataFrame:
        """Convert categorical column to numeric codes and store original categories"""
        try:
            # Convert to category and get codes
            codes = col.astype('category').cat.codes

            # Store the original categories
            categories = col.astype('category').cat.categories
            column_metadata[table_name][col.name]['categories'] = categories

            return pd.DataFrame({col.name: codes})
        except Exception as e:
            self.logger.error(f"Error processing categorical column {col.name}: {str(e)}")
            return pd.DataFrame({col.name: np.zeros(len(col))})

    def transform_table(self, df: pd.DataFrame, table_name: str, column_metadata: Dict[str, Dict[str, Any]]) -> pd.DataFrame:
        """Process a single table with comprehensive validation and logging"""
        # Create a list to store processed DataFrames
        processed_dfs = []

        # Ensure table metadata exists
        if table_name not in column_metadata:
            column_metadata[table_name] = {}

        # Process each column
        for col_name, col in df.items():
            clean_col = self.cleaner.clean_column(col, column_metadata)
            col_type = self.cleaner.detect_column_type(clean_col, column_metadata, table_name)

            # Store metadata with proper table reference
            column_metadata[table_name][col_name] = {
                'original_type': col_type,
                'processed_type': col_type,
            }

            if col_type == 'datetime':
                processed_dfs.append(self.process_datetime(clean_col))
            elif col_type == 'categorical':
                processed_dfs.append(self.process_categorical(clean_col, table_name, column_metadata))
            elif col_type == 'string':
                # For string columns, store as numeric codes with proper category mapping
                unique_vals = clean_col.dropna().unique()
                if len(unique_vals) == 0:
                    # Handle empty columns
                    processed_dfs.append(pd.DataFrame({col_name: [0] * len(clean_col)}))
                    column_metadata[table_name][col_name]['categories'] = ['']
                else:
                    # Create category mapping - filter out extremely long values that are likely corrupted
                    valid_vals = [val for val in unique_vals if isinstance(val, str) and len(val) <= 50]
                    if not valid_vals:
                        # If no valid values, create a simple default based on column name
                        if col_name.lower() in ['campaign_currency', 'mediarow_currency']:
                            valid_vals = ['EUR', 'USD', 'GBP']
                        elif 'name' in col_name.lower():
                            valid_vals = ['Unknown']
                        else:
                            valid_vals = ['N/A']
                    categories = sorted(list(set(valid_vals)))  # Remove duplicates and sort

                    # Apply anonymization for campaign_name and mediaplan_name columns to ensure privacy
                    if col_name.lower() == 'campaign_name':
                        self.logger.info(f"Anonymizing {len(categories)} campaign names for privacy compliance")
                        anonymized_categories = self.anonymize_campaign_names(categories)
                        # Store both original and anonymized for mapping
                        column_metadata[table_name][col_name]['original_categories'] = categories
                        column_metadata[table_name][col_name]['categories'] = anonymized_categories
                        # Create mapping from original to anonymized indices
                        original_to_anon_map = {orig: idx for idx, orig in enumerate(categories)}
                    elif col_name.lower() == 'mediaplan_name':
                        self.logger.info(f"Anonymizing {len(categories)} media plan names while preserving temporal information")
                        anonymized_categories = self.anonymize_media_plan_names(categories)
                        # Store both original and anonymized for mapping
                        column_metadata[table_name][col_name]['original_categories'] = categories
                        column_metadata[table_name][col_name]['categories'] = anonymized_categories
                        # Create mapping from original to anonymized indices
                        original_to_anon_map = {orig: idx for idx, orig in enumerate(categories)}
                    else:
                        # For non-sensitive columns, use original categories
                        column_metadata[table_name][col_name]['categories'] = categories
                        original_to_anon_map = {cat: idx for idx, cat in enumerate(categories)}

                    # Map values to codes, handling missing values
                    codes = clean_col.map(original_to_anon_map).fillna(-1)  # Use -1 for missing

                    processed_dfs.append(pd.DataFrame({col_name: codes}))
            else:  # numeric
                # Ensure numeric columns are properly handled
                if pd.api.types.is_numeric_dtype(clean_col):
                    # Store normalization parameters for denormalization
                    col_min = clean_col.min()
                    col_max = clean_col.max()
                    col_mean = clean_col.mean()
                    col_std = clean_col.std()

                    # Store normalization parameters
                    column_metadata[table_name][col_name]['min'] = col_min
                    column_metadata[table_name][col_name]['max'] = col_max
                    column_metadata[table_name][col_name]['mean'] = col_mean
                    column_metadata[table_name][col_name]['std'] = col_std

                    # Normalize the data (z-score normalization)
                    # Add small epsilon to prevent division by zero warnings
                    if col_std > 1e-8:  # Use small epsilon instead of 0
                        normalized_col = (clean_col - col_mean) / col_std
                    else:
                        # If std is 0 or very small, just center the data
                        normalized_col = clean_col - col_mean

                    processed_dfs.append(pd.DataFrame({col_name: normalized_col}))
                else:
                    # Try to convert to numeric
                    numeric_col = pd.to_numeric(clean_col, errors='coerce').fillna(0)

                    # Store normalization parameters
                    col_min = numeric_col.min()
                    col_max = numeric_col.max()
                    col_mean = numeric_col.mean()
                    col_std = numeric_col.std()

                    column_metadata[table_name][col_name]['min'] = col_min
                    column_metadata[table_name][col_name]['max'] = col_max
                    column_metadata[table_name][col_name]['mean'] = col_mean
                    column_metadata[table_name][col_name]['std'] = col_std

                    # Normalize the data
                    # Add small epsilon to prevent division by zero warnings
                    if col_std > 1e-8:  # Use small epsilon instead of 0
                        normalized_col = (numeric_col - col_mean) / col_std
                    else:
                        # If std is 0 or very small, just center the data
                        normalized_col = numeric_col - col_mean

                    processed_dfs.append(pd.DataFrame({col_name: normalized_col}))

        # Combine all processed DataFrames
        processed_df = pd.concat(processed_dfs, axis=1)

        # Ensure all columns are numeric before returning
        for col_name in processed_df.columns:
            if not pd.api.types.is_numeric_dtype(processed_df[col_name]):
                try:
                    processed_df[col_name] = processed_df[col_name].astype(float)
                except (ValueError, TypeError):
                    self.logger.warning(f"Could not convert column {col_name} to numeric, filling with zeros")
                    processed_df[col_name] = 0.0

        return processed_df
