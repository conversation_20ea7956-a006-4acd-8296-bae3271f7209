import logging
from pathlib import Path
from src.config import settings

logger = logging.getLogger(__name__)

def validate_initial_state(instance):
    """Validate that all required directories and files exist"""
    logger.info("Validating initial state...")
    Path(settings.PATHS['MODELS_DIR']).mkdir(parents=True, exist_ok=True)
    Path(settings.PATHS['PREPROCESSOR_DIR']).mkdir(parents=True, exist_ok=True)
    Path(settings.PATHS['PROCESSED_DIR']).mkdir(parents=True, exist_ok=True)

    # Check if preprocessor exists
    if not Path(settings.PATHS['PREPROCESSOR_PATH']).exists():
        logger.info("Preprocessor not found, will create new one")
    else:
        logger.info("Found existing preprocessor")
