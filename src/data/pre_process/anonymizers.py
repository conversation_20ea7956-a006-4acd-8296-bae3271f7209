"""
Anonymization utilities for sensitive data fields.

This module provides enterprise-grade anonymization methods for different types
of sensitive data while preserving analytical utility.
"""

import hashlib
import random
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)


class BaseAnonymizer:
    """Base class for all anonymizers with common functionality."""

    def __init__(self, seed: int = 42):
        """
        Initialize the anonymizer with a seed for reproducible results.

        Args:
            seed: Random seed for deterministic anonymization
        """
        self.seed = seed
        self.logger = logging.getLogger(self.__class__.__name__)

    def _create_deterministic_hash(self, text: str) -> str:
        """Create a deterministic hash for consistent anonymization."""
        return hashlib.md5(text.encode()).hexdigest()

    def _set_seed_from_hash(self, text_hash: str) -> None:
        """Set random seed based on text hash for deterministic results."""
        random.seed(int(text_hash[:8], 16))


class CampaignNameAnonymizer(BaseAnonymizer):
    """
    Anonymizer for campaign names using hash-based structural anonymization.

    Completely removes company/brand names while maintaining realistic
    campaign naming conventions and structure.
    """

    def __init__(self, seed: int = 42):
        super().__init__(seed)

        # Campaign name components for generating realistic names
        self.prefixes = [
            "Campaign", "Project", "Initiative", "Strategy", "Program", "Launch", "Drive",
            "Push", "Boost", "Focus", "Target", "Reach", "Connect", "Engage", "Activate",
            "Promote", "Brand", "Product", "Service", "Digital", "Mobile", "Online",
            "Social", "Display", "Search", "Video", "Native", "Performance", "Awareness"
        ]

        self.regions = [
            "US", "UK", "DE", "FR", "IT", "ES", "CA", "AU", "JP", "BR", "MX", "CH",
            "AT", "NL", "BE", "SE", "NO", "DK", "FI", "PL", "CZ", "HU", "RO", "GR",
            "Global", "EMEA", "APAC", "LATAM", "NAM", "EU", "DACH", "Nordics"
        ]

        self.campaigns = [
            "Awareness", "Consideration", "Conversion", "Retention", "Acquisition",
            "Performance", "Brand", "Product", "Launch", "Seasonal", "Holiday",
            "Q1", "Q2", "Q3", "Q4", "2024", "2025", "Spring", "Summer", "Fall", "Winter",
            "Always_On", "Retargeting", "Prospecting", "Lookalike", "Custom", "Premium"
        ]

        self.channels = [
            "Display", "Search", "Social", "Video", "Native", "Mobile", "Desktop",
            "Programmatic", "Direct", "Affiliate", "Email", "SMS", "Push", "OOH",
            "Radio", "TV", "Print", "Digital", "Offline", "Online", "Cross_Device"
        ]

    def anonymize(self, categories: List[str]) -> List[str]:
        """
        Anonymize campaign names while maintaining realistic structure and diversity.

        Args:
            categories: List of original campaign names

        Returns:
            List of anonymized campaign names with same length and diversity
        """
        # Validate input categories
        if not categories:
            self.logger.warning("Empty categories list provided for campaign name anonymization")
            return []

        # Clean and validate categories
        clean_categories = []
        for cat in categories:
            if isinstance(cat, str) and len(cat) <= 500:  # Reasonable max length
                clean_categories.append(cat)
            else:
                self.logger.warning(f"Skipping invalid category: {str(cat)[:100]}...")
                clean_categories.append("Invalid_Campaign")

        # Set seed for reproducible anonymization
        random.seed(self.seed)

        anonymized = []
        used_names = set()

        for original in clean_categories:
            # Create a deterministic hash for this campaign name
            name_hash = self._create_deterministic_hash(original)
            self._set_seed_from_hash(name_hash)

            # Generate components based on hash
            prefix = random.choice(self.prefixes)
            region = random.choice(self.regions) if random.random() > 0.3 else ""
            campaign = random.choice(self.campaigns)
            channel = random.choice(self.channels) if random.random() > 0.4 else ""

            # Create name with similar structure to original
            name_parts = [prefix]
            if region:
                name_parts.append(region)
            name_parts.append(campaign)
            if channel:
                name_parts.append(channel)

            # Add year/quarter if original seems to have temporal info
            if any(x in original.lower() for x in ['2020', '2021', '2022', '2023', '2024', '2025']):
                year = random.choice(['2023', '2024', '2025'])
                name_parts.append(year)
            elif any(x in original.lower() for x in ['q1', 'q2', 'q3', 'q4']):
                quarter = random.choice(['Q1', 'Q2', 'Q3', 'Q4'])
                name_parts.append(quarter)

            # Join with underscores (common in campaign names)
            anonymized_name = "_".join(name_parts)

            # Ensure uniqueness
            counter = 1
            base_name = anonymized_name
            while anonymized_name in used_names:
                anonymized_name = f"{base_name}_{counter}"
                counter += 1

            anonymized.append(anonymized_name)
            used_names.add(anonymized_name)

        self.logger.info(f"Anonymized {len(categories)} campaign names for privacy compliance")
        return anonymized


class MediaPlanNameAnonymizer(BaseAnonymizer):
    """
    Anonymizer for media plan names using temporal-preserving selective anonymization.

    Preserves month/year/quarter information for business analysis while
    anonymizing company/brand references.
    """

    def __init__(self, seed: int = 42):
        super().__init__(seed)

        # Month mappings for different languages
        self.month_mappings = {
            # English months
            'january': 'January', 'february': 'February', 'march': 'March', 'april': 'April',
            'may': 'May', 'june': 'June', 'july': 'July', 'august': 'August',
            'september': 'September', 'october': 'October', 'november': 'November', 'december': 'December',

            # Italian months
            'gennaio': 'Gennaio', 'febbraio': 'Febbraio', 'marzo': 'Marzo', 'aprile': 'Aprile',
            'maggio': 'Maggio', 'giugno': 'Giugno', 'luglio': 'Luglio', 'agosto': 'Agosto',
            'settembre': 'Settembre', 'ottobre': 'Ottobre', 'novembre': 'Novembre', 'dicembre': 'Dicembre',

            # Portuguese months
            'janeiro': 'Janeiro', 'fevereiro': 'Fevereiro', 'março': 'Março', 'abril': 'Abril',
            'maio': 'Maio', 'junho': 'Junho', 'julho': 'Julho', 'agosto': 'Agosto',
            'setembro': 'Setembro', 'outubro': 'Outubro', 'novembro': 'Novembro', 'dezembro': 'Dezembro',

            # Spanish months
            'enero': 'Enero', 'febrero': 'Febrero', 'marzo': 'Marzo', 'abril': 'Abril',
            'mayo': 'Mayo', 'junio': 'Junio', 'julio': 'Julio', 'agosto': 'Agosto',
            'septiembre': 'Septiembre', 'octubre': 'Octubre', 'noviembre': 'Noviembre', 'diciembre': 'Diciembre'
        }

        # Generic plan names for anonymization
        self.generic_plan_names = [
            "Media Plan", "Campaign Plan", "Strategy Plan", "Marketing Plan", "Brand Plan",
            "Product Plan", "Launch Plan", "Seasonal Plan", "Holiday Plan", "Performance Plan",
            "Digital Plan", "Social Plan", "Display Plan", "Search Plan", "Video Plan"
        ]

        # Quarter and year patterns
        self.quarters = ['Q1', 'Q2', 'Q3', 'Q4']
        self.years = ['2020', '2021', '2022', '2023', '2024', '2025']

    def _extract_temporal_info(self, original: str) -> tuple[bool, List[str]]:
        """
        Extract temporal information from the original name.

        Args:
            original: Original media plan name

        Returns:
            Tuple of (is_temporal, preserved_parts)
        """
        original_lower = original.lower()
        is_temporal = False
        preserved_parts = []

        # Check for months in different languages
        for month_key, month_value in self.month_mappings.items():
            if month_key in original_lower:
                preserved_parts.append(month_value)
                is_temporal = True
                break

        # Check for years
        for year in self.years:
            if year in original:
                preserved_parts.append(year)
                is_temporal = True

        # Check for quarters
        for quarter in self.quarters:
            if quarter.lower() in original_lower:
                preserved_parts.append(quarter)
                is_temporal = True

        return is_temporal, preserved_parts

    def anonymize(self, categories: List[str]) -> List[str]:
        """
        Anonymize media plan names while preserving temporal information.

        Args:
            categories: List of original media plan names

        Returns:
            List of anonymized media plan names preserving temporal information
        """
        # Validate input categories
        if not categories:
            self.logger.warning("Empty categories list provided for media plan name anonymization")
            return []

        # Clean and validate categories
        clean_categories = []
        for cat in categories:
            if isinstance(cat, str) and len(cat) <= 500:  # Reasonable max length
                clean_categories.append(cat)
            else:
                self.logger.warning(f"Skipping invalid media plan category: {str(cat)[:100]}...")
                clean_categories.append("Invalid_Plan")

        # Set seed for reproducible anonymization
        random.seed(self.seed)

        anonymized = []
        used_names = set()
        temporal_preserved = 0

        for original in clean_categories:
            # Create deterministic hash for this media plan name
            name_hash = self._create_deterministic_hash(original)
            self._set_seed_from_hash(name_hash)

            # Extract temporal information
            is_temporal, preserved_parts = self._extract_temporal_info(original)

            # If it's primarily temporal (just month/year/quarter), preserve it
            if is_temporal and len(original.split()) <= 3:
                # For simple temporal names, keep them as-is or slightly modify
                if len(preserved_parts) == 1:
                    anonymized_name = preserved_parts[0]
                else:
                    anonymized_name = " ".join(preserved_parts)
                temporal_preserved += 1
            else:
                # For complex names with company/brand info, anonymize but preserve temporal parts
                base_name = random.choice(self.generic_plan_names)

                if preserved_parts:
                    # Combine generic name with preserved temporal info
                    anonymized_name = f"{base_name} - {' '.join(preserved_parts)}"
                    temporal_preserved += 1
                else:
                    # No temporal info, just use generic name
                    anonymized_name = base_name

            # Ensure uniqueness
            counter = 1
            base_anonymized = anonymized_name
            while anonymized_name in used_names:
                anonymized_name = f"{base_anonymized} {counter}"
                counter += 1

            anonymized.append(anonymized_name)
            used_names.add(anonymized_name)

        preservation_rate = (temporal_preserved / len(categories)) * 100
        self.logger.info(
            f"Anonymized {len(categories)} media plan names while preserving temporal information "
            f"({preservation_rate:.1f}% temporal preservation rate)"
        )

        return anonymized


class AnonymizerFactory:
    """Factory class for creating appropriate anonymizers."""

    @staticmethod
    def get_anonymizer(column_name: str, **kwargs) -> BaseAnonymizer:
        """
        Get the appropriate anonymizer for a given column.

        Args:
            column_name: Name of the column to anonymize
            **kwargs: Additional arguments for the anonymizer

        Returns:
            Appropriate anonymizer instance
        """
        column_lower = column_name.lower()

        if column_lower == 'campaign_name':
            return CampaignNameAnonymizer(**kwargs)
        elif column_lower == 'mediaplan_name':
            return MediaPlanNameAnonymizer(**kwargs)
        else:
            raise ValueError(f"No anonymizer available for column: {column_name}")
