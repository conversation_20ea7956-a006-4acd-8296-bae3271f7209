import logging
from pathlib import Path
from typing import Dict, Any
import pickle
from src.config import settings

logger = logging.getLogger(__name__)

def save_preprocessor_state(instance):
    """Save the preprocessor state to disk"""
    logger.info("Saving preprocessor state...")
    preprocessor_path = settings.PATHS['PREPROCESSOR_PATH']
    with open(preprocessor_path, 'wb') as f:
        pickle.dump(instance, f)
    logger.info(f"Saved preprocessor state to {preprocessor_path}")

def load_preprocessor(cls, config):
    """Load preprocessor from disk"""
    preprocessor_path = settings.PATHS['PREPROCESSOR_PATH']
    if Path(preprocessor_path).exists():
        logger.info("Loading preprocessor from disk...")
        with open(preprocessor_path, 'rb') as f:
            preprocessor = pickle.load(f)
        logger.info(f"Loaded preprocessor state from {preprocessor_path}")
        return preprocessor
    return cls()
