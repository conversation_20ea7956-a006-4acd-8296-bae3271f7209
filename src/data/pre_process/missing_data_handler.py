import pandas as pd
import numpy as np
import logging
from typing import Dict, Any
import json


class MissingDataHandler:
    """
    Handles realistic missing data patterns in synthetic data generation.
    
    Analyzes missing data patterns from real data and reproduces them in synthetic data.
    """
    
    def __str__(self) -> str:
        return "MISSING-DATA-HANDLER"
    
    def __init__(self):
        """Initialize missing data handler"""
        self.logger = logging.getLogger(str(self))
        self.missing_patterns = {}
        
    def analyze_missing_patterns(self, data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Analyze missing data patterns from real data
        
        Args:
            data: Dictionary of real DataFrames
            
        Returns:
            Dictionary containing missing data patterns for each table/column
        """
        patterns = {}
        
        for table_name, df in data.items():
            self.logger.info(f"Analyzing missing patterns for {table_name} table")
            table_patterns = {}
            
            for col in df.columns:
                # Calculate missing percentage
                missing_count = df[col].isna().sum()
                total_count = len(df)
                missing_percentage = (missing_count / total_count) * 100
                
                # Store simple missing pattern
                if missing_count > 0:
                    table_patterns[col] = {
                        'missing_percentage': missing_percentage,
                        'missing_count': int(missing_count),
                        'total_count': int(total_count),
                        'has_missing': True
                    }
                    
                    self.logger.info(f"  {col}: {missing_percentage:.1f}% missing ({missing_count}/{total_count})")
                else:
                    table_patterns[col] = {
                        'missing_percentage': 0.0,
                        'missing_count': 0,
                        'total_count': int(total_count),
                        'has_missing': False
                    }
            
            patterns[table_name] = table_patterns
        
        self.missing_patterns = patterns
        return patterns
    

    
    def apply_missing_patterns(self, synthetic_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Apply realistic missing patterns to synthetic data
        
        Args:
            synthetic_data: Dictionary of synthetic DataFrames
            
        Returns:
            Dictionary of DataFrames with realistic missing patterns applied
        """
        if not self.missing_patterns:
            self.logger.warning("No missing patterns available. Run analyze_missing_patterns first.")
            return synthetic_data
        
        result_data = {}
        
        for table_name, df in synthetic_data.items():
            if table_name not in self.missing_patterns:
                self.logger.warning(f"No missing patterns found for table {table_name}")
                result_data[table_name] = df.copy()
                continue
                
            self.logger.info(f"Applying missing patterns to {table_name} table")
            result_df = df.copy()
            table_patterns = self.missing_patterns[table_name]
            
            # Apply missing patterns column by column
            for col in result_df.columns:
                if col in table_patterns and table_patterns[col]['has_missing']:
                    pattern = table_patterns[col]
                    result_df = self._apply_column_missing_pattern(result_df, col, pattern)
            
            result_data[table_name] = result_df
            
        return result_data
    
    def _apply_column_missing_pattern(self, df: pd.DataFrame, col: str, pattern: Dict[str, Any]) -> pd.DataFrame:
        """Apply missing pattern to a specific column"""
        missing_percentage = pattern['missing_percentage']

        if missing_percentage <= 0:
            return df

        # Skip date columns - they should never be empty
        if self._is_date_column(col):
            self.logger.info(f"  Skipping missing pattern for date column: {col}")
            return df

        # Calculate how many values should be missing
        total_rows = len(df)
        missing_count = int((missing_percentage / 100) * total_rows)

        if missing_count <= 0:
            return df

        # Randomly select rows to make missing
        np.random.seed(42)  # For reproducibility
        missing_indices = np.random.choice(total_rows, size=missing_count, replace=False)

        # Apply missing values
        df_copy = df.copy()

        # Set values to empty string or NaN based on column type
        if df_copy[col].dtype == 'object':
            # For string columns, always use empty string (not <NA>)
            df_copy.loc[missing_indices, col] = ''
        else:
            # For numeric columns, use NaN
            df_copy.loc[missing_indices, col] = np.nan

        self.logger.info(f"  Applied {len(missing_indices)} missing values to {col} ({missing_percentage:.1f}%)")
        return df_copy

    def _is_date_column(self, col: str) -> bool:
        """Check if a column is a date column that should never be empty"""
        date_keywords = ['date', 'start', 'end', 'timestamp', 'time']
        col_lower = col.lower()
        return any(keyword in col_lower for keyword in date_keywords)
    

    
    def save_patterns(self, filepath: str):
        """Save missing patterns to file"""
        try:
            with open(filepath, 'w') as f:
                json.dump(self.missing_patterns, f, indent=2)
            self.logger.info(f"Missing patterns saved to {filepath}")
        except Exception as e:
            self.logger.error(f"Failed to save missing patterns: {str(e)}")
    
    def load_patterns(self, filepath: str):
        """Load missing patterns from file"""
        try:
            with open(filepath, 'r') as f:
                self.missing_patterns = json.load(f)
            self.logger.info(f"Missing patterns loaded from {filepath}")
        except Exception as e:
            self.logger.error(f"Failed to load missing patterns: {str(e)}")

    def get_missing_summary(self) -> Dict[str, Any]:
        """Get summary of missing patterns"""
        if not self.missing_patterns:
            return {}

        summary = {}
        for table_name, table_patterns in self.missing_patterns.items():
            table_summary = {
                'total_columns': len(table_patterns),
                'columns_with_missing': sum(1 for p in table_patterns.values() if p.get('has_missing', False)),
                'average_missing_percentage': np.mean([p.get('missing_percentage', 0) for p in table_patterns.values()])
            }
            summary[table_name] = table_summary

        return summary
    

