import pandas as pd
from typing import Dict, Any
import logging
from src.config import settings

logger = logging.getLogger(__name__)


class DataCleaner:
    """Class responsible for cleaning and basic data type conversion"""

    def __str__(self) -> str:
        return "DATA-CLEANER"

    def __init__(self):
        self.logger = logging.getLogger(str(self))

    def clean_column(self, series: pd.Series, _column_metadata: Dict[str, Dict[str, Any]], preserve_missing: bool = True) -> pd.Series:
        """Handle basic data type conversion while optionally preserving missing data patterns"""
        try:
            # If preserving missing patterns, only do minimal cleaning
            if preserve_missing:
                # Convert datetime columns to numeric timestamps if they exist
                if pd.api.types.is_datetime64_any_dtype(series):
                    return series.astype('int64') // 10**9  # Convert to seconds since epoch

                # For object/string columns, keep them as-is (including missing values)
                if pd.api.types.is_object_dtype(series):
                    return series

                # For numeric columns, keep missing values as NaN
                if pd.api.types.is_numeric_dtype(series):
                    return series

                # For categorical columns, preserve them (including missing)
                if pd.api.types.is_categorical_dtype(series):
                    return series

                # Default: try to convert to numeric but preserve NaN
                try:
                    return pd.to_numeric(series, errors='coerce')
                except:
                    return series

            # Original aggressive cleaning (for backward compatibility)
            # Handle missing values first
            if len(series.dropna()) == 0:
                return series.fillna(0)

            # Convert datetime columns to numeric timestamps if they exist
            if pd.api.types.is_datetime64_any_dtype(series):
                return series.astype('int64') // 10**9  # Convert to seconds since epoch

            # For object/string columns, keep them as-is for now
            # The type detection and transformation will handle the encoding
            if pd.api.types.is_object_dtype(series):
                # Fill missing values with empty string (not "MISSING")
                return series.fillna('')

            # Handle missing values by filling with median for numeric columns
            if pd.api.types.is_numeric_dtype(series):
                if len(series.dropna()) > 0:
                    series = series.fillna(series.median())
                else:
                    series = series.fillna(0)
                return series

            # For categorical columns, preserve them
            if pd.api.types.is_categorical_dtype(series):
                return series.fillna('')

            # If still not handled, try to convert to numeric as last resort
            try:
                return pd.to_numeric(series, errors='coerce').fillna(0)
            except:
                return series.fillna('')

        except Exception as e:
            self.logger.error(f"Error cleaning column {series.name}: {str(e)}")
            # For any column, use 0 as default
            return pd.Series(0, index=series.index)

    def detect_column_type(self, col: pd.Series, column_metadata: Dict[str, Dict[str, Any]], table_name: str = 'main') -> str:
        """Automatically detect column data type and track categorical columns"""
        col_name = str(col.name).lower()

        # Ensure table metadata exists
        if table_name not in column_metadata:
            column_metadata[table_name] = {}

        # Get string columns from settings
        string_columns = settings.PREPROCESSING_PARAMS["string_columns"]

        # Check for explicitly defined string columns FIRST (including IDs that are strings of numbers)
        if col_name in string_columns:
            return 'string'

        # Numeric detection - if it's already numeric and not a string column, keep as numeric
        if pd.api.types.is_numeric_dtype(col):
            return 'numeric'

        # Date detection - be more specific about what constitutes a date column
        if ('date' in col_name and 'budget' not in col_name) or 'time' in col_name:
            try:
                # Try to parse as datetime and check if most values are valid dates
                parsed_dates = pd.to_datetime(col, errors='coerce')
                valid_count = len(parsed_dates.dropna())
                if valid_count / len(col) > 0.5:
                    return 'datetime'
            except:
                pass

        # Also check if the column contains date-like strings
        if pd.api.types.is_object_dtype(col):
            # Sample a few values to check if they look like dates
            sample_vals = col.dropna().head(10).astype(str)
            date_like_count = 0
            for val in sample_vals:
                try:
                    pd.to_datetime(val)
                    date_like_count += 1
                except:
                    pass
            if date_like_count >= len(sample_vals) * 0.8:  # 80% look like dates
                return 'datetime'

        # Check if it's a known string column or has string-like content
        if (col_name in string_columns or
            pd.api.types.is_object_dtype(col) or
            pd.api.types.is_categorical_dtype(col)):

            # For explicitly listed string columns, always treat as string
            # This includes IDs that are strings of numbers
            if col_name in string_columns:
                return 'string'

            # For other object/categorical columns, check if they're truly numeric
            try:
                # Try to convert to numeric - if it works and has no text, might be numeric
                numeric_version = pd.to_numeric(col, errors='coerce')
                valid_count = len(numeric_version.dropna())
                if valid_count > 0:
                    non_na_ratio = valid_count / len(col)

                if non_na_ratio > 0.95 and not any('_id' in col_name.lower() for _ in [1]):  # Very high conversion rate and not an ID
                        # Check if values look like they should be strings (e.g., have leading zeros, are IDs)
                        sample_vals = col.dropna().astype(str).head(10)
                        has_leading_zeros = any(val.startswith('0') and len(val) > 1 for val in sample_vals)
                        if not has_leading_zeros:
                            return 'numeric'
            except:
                pass

            # It's a string/categorical column
            unique_vals = col.dropna().unique()
            if len(unique_vals) > 0:
                return 'string'
            else:
                return 'categorical'

        # Try to convert to numeric for non-numeric columns
        try:
            numeric_version = pd.to_numeric(col, errors='coerce')
            non_na_ratio = len(numeric_version.dropna()) / len(col)
            if non_na_ratio > 0.8:  # 80% of values are numeric
                return 'numeric'
        except (ValueError, TypeError):
            pass

        # If it can't be converted to numeric, treat as string
        return 'string'
