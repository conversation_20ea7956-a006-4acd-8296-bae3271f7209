import logging
from typing import Any, Dict, List, Tuple

import pandas as pd
from sklearn.preprocessing import <PERSON><PERSON>ncoder, MinMaxScaler

from .cleaners import DataCleaner
from .inverse import inverse_transform
from .io import load_preprocessor, save_preprocessor_state
from .missing_data_handler import MissingDataH<PERSON><PERSON>
from .transformers import DataTransformer
from .validators import validate_initial_state


class DataPreprocessor:

    def __str__(self) -> str:
        return "DATA-PREPROCESSOR"

    def __init__(self):
        """Initialize preprocessor with stateful transformers and metadata tracking"""
        self.logger = logging.getLogger(str(self))

        self.label_encoders: Dict[str, LabelEncoder] = {}
        self.scalers: Dict[str, MinMaxScaler] = {}
        self.column_metadata: Dict[str, Dict[str, Any]] = {
            'main': {},
            'performance': {},
            'conversions': {}
        }
        self.feature_ranges: Dict[str, Tuple[float, float]] = {
            'numeric': (0, 1),  # MinMaxScaler normalizes to [0,1]
            'categorical': (-1, 1)  # For consistency with GAN training
        }

        # Initialize component classes
        self.cleaner = DataCleaner()
        self.transformer = DataTransformer()
        self.missing_data_handler = MissingDataHandler()

        self._validate_initial_state()

        # Track categorical columns
        self.categorical_columns: Dict[str, List[str]] = {
            'main': [],
            'performance': [],
            'conversions': []
        }

        # Track whether to preserve missing data patterns
        self.preserve_missing_patterns = True

    def _validate_initial_state(self):
        """Validate that all required directories and files exist"""
        validate_initial_state(self)

    def _clean_column(self, series: pd.Series) -> pd.Series:
        """Handle basic data type conversion while maintaining raw data structure"""
        return self.cleaner.clean_column(series, self.column_metadata, preserve_missing=self.preserve_missing_patterns)

    def _detect_column_type(self, col: pd.Series) -> str:
        """Automatically detect column data type and track categorical columns"""
        return self.cleaner.detect_column_type(col, self.column_metadata)

    def _transform_table(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """Process a single table with comprehensive validation and logging"""
        return self.transformer.transform_table(df, table_name, self.column_metadata)

    def analyze_missing_patterns(self, main_df: pd.DataFrame,
                                performance_df: pd.DataFrame,
                                conversions_df: pd.DataFrame):
        """
        Analyze missing data patterns from real data

        Args:
            main_df: Main table data
            performance_df: Performance metrics data
            conversions_df: Conversion data
        """
        self.logger.info("Analyzing missing data patterns from real data")
        data = {
            'main': main_df,
            'performance': performance_df,
            'conversions': conversions_df
        }

        self.missing_data_handler.analyze_missing_patterns(data)

        # Save patterns for later use
        self.missing_data_handler.save_patterns("models_local/missing_patterns.json")

        # Log summary
        summary = self.missing_data_handler.get_missing_summary()
        for table_name, table_summary in summary.items():
            self.logger.info(f"{table_name}: {table_summary['columns_with_missing']}/{table_summary['total_columns']} columns have missing data")

    def fit_transform(self, main_df: pd.DataFrame,
                    performance_df: pd.DataFrame,
                    conversions_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Process all input tables with proper numeric conversion

        Args:
            main_df: Main table data
            performance_df: Performance metrics data
            conversions_df: Conversion data

        Returns:
            Tuple of processed DataFrames (main, performance, conversions)
        """
        self.logger.info("Starting data preprocessing for GAN training")

        # Remove problematic pandas option that causes NA ambiguity
        # pd.set_option('mode.use_inf_as_na', True)  # This causes the boolean NA error

        # Skip missing pattern analysis for now to fix training pipeline
        # TODO: Debug and fix the pandas NA issue in missing pattern analysis
        self.logger.info("Skipping missing pattern analysis to avoid pandas NA issues")
        # if self.preserve_missing_patterns:
        #     try:
        #         self.analyze_missing_patterns(main_df, performance_df, conversions_df)
        #     except Exception as e:
        #         self.logger.warning(f"Missing pattern analysis failed: {str(e)}")
        #         self.logger.warning("Continuing without missing pattern analysis")

        # Process each table
        processed_tables = {}
        for name, raw_df in zip(['main', 'performance', 'conversions'],
                               [main_df, performance_df, conversions_df]):
            self.logger.info(f"Processing {name} table (shape: {raw_df.shape})")
            processed_df = self._transform_table(raw_df, name)
            processed_tables[name] = processed_df

            # Log processing summary with safe min/max calculation
            try:
                # Use pandas methods that handle NA values properly
                min_val = processed_df.min().min()
                max_val = processed_df.max().max()
                self.logger.info(
                    f"Processed {name} table. "
                    f"Value range: [{min_val:.4f}, {max_val:.4f}]"
                )
            except Exception:
                # Fallback if min/max calculation fails
                self.logger.info(f"Processed {name} table. Shape: {processed_df.shape}")

        return (
            processed_tables['main'],
            processed_tables['performance'],
            processed_tables['conversions']
        )

    def apply_missing_patterns_to_synthetic(self, synthetic_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Apply realistic missing patterns to synthetic data

        Args:
            synthetic_data: Dictionary of synthetic DataFrames

        Returns:
            Dictionary of DataFrames with realistic missing patterns applied
        """
        # Backward compatibility: check if preserve_missing_patterns exists
        if not hasattr(self, 'preserve_missing_patterns'):
            self.logger.info("Backward compatibility: preserve_missing_patterns not found, initializing missing data handler")
            self.preserve_missing_patterns = True
            self.missing_data_handler = MissingDataHandler()

        if not self.preserve_missing_patterns:
            return synthetic_data

        # Ensure missing_data_handler exists (backward compatibility)
        if not hasattr(self, 'missing_data_handler'):
            self.missing_data_handler = MissingDataHandler()

        # Load missing patterns if they exist
        try:
            self.missing_data_handler.load_patterns("models_local/missing_patterns.json")
            self.logger.info("Applying realistic missing patterns to synthetic data")
            return self.missing_data_handler.apply_missing_patterns(synthetic_data)
        except FileNotFoundError:
            self.logger.info("No missing patterns file found, skipping missing pattern application")
            return synthetic_data
        except Exception as e:
            self.logger.warning(f"Could not apply missing patterns: {str(e)}")
            return synthetic_data

    def save_state(self):
        """Save the preprocessor state to disk"""
        save_preprocessor_state(self)

    @classmethod
    def load(cls):
        """Load preprocessor from disk"""
        return load_preprocessor(cls, None)  # Will update load_preprocessor to use PATHS directly

    def inverse_transform(self, processed_df: pd.DataFrame, table_name: str = 'main') -> pd.DataFrame:
        """Convert processed data back to its original format"""
        return inverse_transform(processed_df, table_name, self.column_metadata)
