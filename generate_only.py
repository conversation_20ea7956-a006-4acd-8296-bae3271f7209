#!/usr/bin/env python3
"""
Generation Only Pipeline - Generate synthetic data from existing models
"""

from src.app.main import SyntheticData

if __name__ == '__main__':
    payload = {
        'run_type': 'generation',
        'client_id': None,
        'campaign_id': None,
        'mediaplan_id': None
    }

    result = SyntheticData().main(payload)
    print(f"Generation completed with status: {result.status}")
