#!/usr/bin/env python3
"""
Quick debugging script for testing training pipeline with cached data
"""

import pickle
import os
from src.app.components.input import InputComponent
from src.app.components.training import TrainingComponent
from src.models.models import PayloadSchema

def test_training_pipeline():
    """Test the full training pipeline with cached data"""
    print("🔍 Training Pipeline Debug Script")
    print("=" * 50)
    
    # Create a dummy payload
    payload = PayloadSchema(
        run_type="training",
        client_ids=[],
        campaign_ids=[],
        mediarow_ids=[],
        nrows=None
    )
    
    try:
        # Test input component (should use cached data)
        print("1️⃣ Testing InputComponent...")
        input_component = InputComponent(payload=payload)
        input_result = input_component.load_training_data()
        
        if input_result['status'] != 'success':
            print(f"❌ Input failed: {input_result.get('error', 'Unknown error')}")
            return False
            
        print("✅ Input component successful!")
        print(f"   Data shapes: main={input_result['data']['main'].shape}, "
              f"perf={input_result['data']['performance'].shape}, "
              f"conv={input_result['data']['conversions'].shape}")
        
        # Test training component
        print("\n2️⃣ Testing TrainingComponent...")
        training_component = TrainingComponent(input_data=input_result)
        training_result = training_component.run_training(joint_training=True)
        
        if training_result['status'] != 'success':
            print(f"❌ Training failed: {training_result.get('error', 'Unknown error')}")
            return False
            
        print("✅ Training component successful!")
        return True
        
    except Exception as e:
        print(f"❌ Pipeline failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    cache_file = "data/cache/raw_training_data.pkl"
    
    if not os.path.exists(cache_file):
        print("❌ No cached data found!")
        print("Run 'python dry_run.py' or the Streamlit app once to cache data.")
        return
    
    success = test_training_pipeline()
    
    if success:
        print("\n🎉 Training pipeline works!")
    else:
        print("\n💡 Fix the issue and run this script again.")

if __name__ == "__main__":
    main()
