from src.app.main import SyntheticData
import sys

def show_help():
    """Show available commands"""
    print("🚀 Synthetic Data Pipeline Commands:")
    print("")
    print("📊 Standard Pipeline:")
    print("  python dry_run.py training           # Train models only")
    print("  python dry_run.py generation         # Generate data only (requires trained models)")
    print("  python dry_run.py evaluation         # Evaluate existing data")
    print("  python dry_run.py full_pipeline      # Complete pipeline (default)")
    print("")
    print("🎯 Conditional Pipeline (Media → Performance + Conversions):")
    print("  python dry_run.py conditional_training    # Train conditional model")
    print("  python dry_run.py conditional_generation  # Generate using conditional model")
    print("  python dry_run.py conditional_pipeline    # Complete conditional pipeline")
    print("")
    print("💡 Examples:")
    print("  python dry_run.py                    # Run full standard pipeline")
    print("  python dry_run.py conditional_pipeline  # Run full conditional pipeline")

if __name__ == '__main__':
    # Check for help
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
        sys.exit(0)

    # Default to full pipeline, but allow override
    run_type = sys.argv[1] if len(sys.argv) > 1 else 'full_pipeline'

    # Validate run_type
    valid_run_types = [
        'training', 'generation', 'evaluation', 'full_pipeline',
        'conditional_training', 'conditional_generation', 'conditional_pipeline'
    ]

    if run_type not in valid_run_types:
        print(f"❌ Error: Invalid run_type '{run_type}'")
        print(f"✅ Valid options: {', '.join(valid_run_types)}")
        print("\nRun 'python dry_run.py help' for more information.")
        sys.exit(1)

    payload = {
        'run_type': run_type,
        'client_id': None,
        'campaign_id': None,
        'mediaplan_id': None
    }

    print(f"🚀 Starting {run_type.replace('_', ' ').title()} Pipeline...")
    result = SyntheticData().main(payload)

    if result.status == 'success':
        print(f"✅ {run_type.replace('_', ' ').title()} completed successfully!")
    else:
        print(f"❌ {run_type.replace('_', ' ').title()} failed: {getattr(result, 'error', 'Unknown error')}")


