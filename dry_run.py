import logging
import sys

from src.app.main import SyntheticData

if __name__ == '__main__':
    # Default to full pipeline, but allow override
    run_type = sys.argv[1] if len(sys.argv) > 1 else 'full_pipeline'

    payload = {
        'run_type': run_type,
        'client_id': None,
        'campaign_id': None,
        'mediaplan_id': None
    }

    result = SyntheticData().main(payload)
    logger = logging.getLogger("DRY_RUN")
    logger.info(f"{run_type.replace('_', ' ').title()} completed with status: {result.status}")


