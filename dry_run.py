import logging
import sys

from src.app.main import SyntheticData

if __name__ == '__main__':
    # Simplified commands:
    # python dry_run.py training    - Train conditional GAN models
    # python dry_run.py generation  - Generate synthetic data using existing models
    run_type = sys.argv[1] if len(sys.argv) > 1 else 'training'

    payload = {
        'run_type': run_type,
        'client_id': None,
        'campaign_id': None,
        'mediaplan_id': None
    }

    result = SyntheticData().main(payload)
    logger = logging.getLogger("DRY_RUN")
    logger.info(f"Conditional {run_type} completed with status: {result.status}")


