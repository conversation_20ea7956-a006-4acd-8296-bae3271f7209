#!/usr/bin/env python3
"""
Quick debugging script for testing preprocessing fixes without reloading data
"""

import pickle
import os
import sys
from src.data.pre_process.main import DataPreprocessor

def load_cached_data():
    """Load cached training data"""
    cache_file = "data/cache/raw_training_data.pkl"
    
    if not os.path.exists(cache_file):
        print("❌ No cached data found!")
        print("Run the full pipeline once to cache the data, then use this script for debugging.")
        return None
    
    print("🚀 Loading cached data...")
    with open(cache_file, 'rb') as f:
        data = pickle.load(f)
    
    print(f"✅ Loaded cached data:")
    print(f"  - Main: {data['main'].shape}")
    print(f"  - Performance: {data['performance'].shape}")
    print(f"  - Conversions: {data['conversions'].shape}")
    
    return data

def test_preprocessing(data):
    """Test preprocessing on cached data"""
    print("\n🔄 Testing preprocessing...")
    print("=" * 50)
    
    try:
        preprocessor = DataPreprocessor()
        
        main_df, performance_df, conversions_df = preprocessor.fit_transform(
            data['main'], 
            data['performance'], 
            data['conversions']
        )
        
        print("✅ Preprocessing successful!")
        print(f"  - Main: {main_df.shape}")
        print(f"  - Performance: {performance_df.shape}")
        print(f"  - Conversions: {conversions_df.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Preprocessing failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debugging function"""
    print("🔍 Preprocessing Debug Script")
    print("=" * 50)
    
    # Load cached data
    data = load_cached_data()
    if data is None:
        return
    
    # Test preprocessing
    success = test_preprocessing(data)
    
    if success:
        print("\n🎉 Preprocessing works! You can now test training.")
    else:
        print("\n💡 Fix the preprocessing issue and run this script again.")

if __name__ == "__main__":
    main()
