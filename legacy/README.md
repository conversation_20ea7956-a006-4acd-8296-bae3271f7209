# 📁 Legacy Multi-Table GAN Implementation

This folder contains the original multi-table GAN implementation that was developed before the pivot to conditional training as the primary approach.

## 🗂️ Contents

- **`gan/multi_table_gan.py`** - Original multi-table GAN with separate generators/discriminators for each table
- **`training/train.py`** - Original training pipeline for multi-table approach
- **`generation/generate.py`** - Original generation pipeline for multi-table approach

## 🎯 Original Architecture

The legacy system used a **multi-table approach** where:
- Separate generators for media_dim, performance, and conversions tables
- Separate discriminators for each table
- Joint training with synchronized batches
- Independent generation of all tables simultaneously

## 🔄 Why Moved to Legacy

The product direction changed to focus on **conditional training** as the primary approach:
- Conditional training provides better referential integrity
- Media-driven generation offers more control
- Simpler command structure requested by management
- Conditional approach better matches business use cases

## 🛡️ Preservation

This code is preserved for:
- **Reference purposes** - Understanding the evolution of the system
- **Potential future use** - If multi-table approach is needed again
- **Learning** - Comparing different GAN architectures
- **Backup** - Complete working implementation

## ⚠️ Status

**LEGACY CODE - NOT ACTIVELY MAINTAINED**

The current active implementation is in the main `src/` directory using conditional GANs.

---

*Preserved on: $(date)*
*Original implementation by: Development Team*
*Reason for archival: Product pivot to conditional training focus*
